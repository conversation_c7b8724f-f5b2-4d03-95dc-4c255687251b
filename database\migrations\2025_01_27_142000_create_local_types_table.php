<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create local_types table
        Schema::create('local_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('name_fr')->nullable(); // French name
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('description')->nullable();
            $table->string('icon')->default('fas fa-building'); // FontAwesome icon
            $table->string('color')->default('#007bff'); // Color for UI
            $table->boolean('active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Insert default types
        $defaultTypes = [
            ['name' => 'Office', 'name_fr' => 'Bureau', 'icon' => 'fas fa-briefcase', 'color' => '#007bff'],
            ['name' => 'Meeting Room', 'name_fr' => 'Salle de réunion', 'icon' => 'fas fa-users', 'color' => '#28a745'],
            ['name' => 'Event Space', 'name_fr' => 'Espace événementiel', 'icon' => 'fas fa-calendar-alt', 'color' => '#dc3545'],
            ['name' => 'Coworking Space', 'name_fr' => 'Espace de coworking', 'icon' => 'fas fa-laptop', 'color' => '#ffc107'],
            ['name' => 'Studio', 'name_fr' => 'Studio', 'icon' => 'fas fa-camera', 'color' => '#6f42c1'],
            ['name' => 'Warehouse', 'name_fr' => 'Entrepôt', 'icon' => 'fas fa-warehouse', 'color' => '#fd7e14'],
            ['name' => 'sport', 'name_fr' => 'Sport', 'icon' => 'fas fa-dumbbell', 'color' => '#20c997'],
            ['name' => 'conference', 'name_fr' => 'Conférence', 'icon' => 'fas fa-microphone', 'color' => '#6610f2'],
            ['name' => 'fête', 'name_fr' => 'Fête', 'icon' => 'fas fa-birthday-cake', 'color' => '#e83e8c'],
        ];

        foreach ($defaultTypes as $index => $type) {
            $type['sort_order'] = $index + 1;
            $type['created_at'] = now();
            $type['updated_at'] = now();
            DB::table('local_types')->insert($type);
        }

        // Modify locals table to remove enum constraint and use string
        DB::statement("ALTER TABLE locals MODIFY COLUMN type VARCHAR(100)");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore enum constraint
        DB::statement("ALTER TABLE locals MODIFY COLUMN type ENUM('sport', 'conference', 'fête', 'Office', 'Meeting Room', 'Event Space', 'Coworking Space', 'Studio', 'Warehouse', 'Other') DEFAULT 'Other'");
        
        Schema::dropIfExists('local_types');
    }
};
