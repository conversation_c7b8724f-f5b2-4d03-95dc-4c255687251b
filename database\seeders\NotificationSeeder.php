<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Récupérer les utilisateurs
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('Aucun utilisateur trouvé. Veuillez d\'abord exécuter UserSeeder.');
            return;
        }

        $notifications = [
            [
                'content' => 'Votre réservation du 15/12/2024 a été confirmée',
                'type' => 'success',
            ],
            [
                'content' => 'Nouveau local disponible : Salle de conférence Premium',
                'type' => 'info',
            ],
            [
                'content' => 'Votre paiement de 150MAD a été traité avec succès',
                'type' => 'payment',
            ],
            [
                'content' => 'Rappel : Votre réservation commence dans 2 heures',
                'type' => 'warning',
            ],
            [
                'content' => 'Votre réservation du 20/12/2024 a été annulée',
                'type' => 'error',
            ],
            [
                'content' => 'Nouvelle fonctionnalité : Scanner QR pour check-in rapide',
                'type' => 'info',
            ],
            [
                'content' => 'Maintenance programmée le 25/12/2024 de 2h à 4h',
                'type' => 'admin',
            ],
            [
                'content' => 'Votre facture #123 est maintenant disponible',
                'type' => 'info',
            ],
        ];

        foreach ($users as $user) {
            // Créer 3-5 notifications par utilisateur
            $userNotifications = collect($notifications)->random(rand(3, 5));
            
            foreach ($userNotifications as $notificationData) {
                Notification::create([
                    'user_id' => $user->id,
                    'content' => $notificationData['content'],
                    'type' => $notificationData['type'],
                    'is_read' => rand(0, 1) === 1, // 50% de chance d'être lue
                    'created_at' => now()->subMinutes(rand(1, 1440)), // Entre 1 minute et 24h
                ]);
            }
        }

        $this->command->info('Notifications créées avec succès !');
    }
}
