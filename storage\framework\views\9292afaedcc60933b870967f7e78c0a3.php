<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-user me-2"></i>Mon profil
            </h1>
            <p class="text-muted">Gérez vos informations personnelles et votre QR code</p>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Informations personnelles
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('profile.update')); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Name -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom complet
                                </label>
                                <input type="text"
                                       class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="name"
                                       name="name"
                                       value="<?php echo e(old('name', $user->name)); ?>"
                                       required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Adresse email
                                </label>
                                <input type="email"
                                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="email"
                                       name="email"
                                       value="<?php echo e(old('email', $user->email)); ?>"
                                       required>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Role Display -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Rôle
                                </label>
                                <div class="form-control-plaintext">
                                    <?php if($user->isAdmin()): ?>
                                        <span class="badge bg-danger">Administrateur</span>
                                    <?php else: ?>
                                        <span class="badge bg-primary">Client</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Membre depuis
                                </label>
                                <div class="form-control-plaintext">
                                    <?php echo e($user->created_at->format('d/m/Y')); ?>

                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Password Change -->
                        <h6 class="mb-3">
                            <i class="fas fa-lock me-2"></i>Changer le mot de passe
                        </h6>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="current_password" class="form-label">Mot de passe actuel</label>
                                <input type="password"
                                       class="form-control <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="current_password"
                                       name="current_password">
                                <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-4">
                                <label for="password" class="form-label">Nouveau mot de passe</label>
                                <input type="password"
                                       class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="password"
                                       name="password">
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-4">
                                <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
                                <input type="password"
                                       class="form-control"
                                       id="password_confirmation"
                                       name="password_confirmation">
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Mettre à jour le profil
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques du compte
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo e($user->reservations()->count()); ?></h4>
                                <small class="text-muted">Réservations totales</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success"><?php echo e($user->reservations()->confirmed()->count()); ?></h4>
                                <small class="text-muted">Confirmées</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-warning"><?php echo e($user->reservations()->pending()->count()); ?></h4>
                                <small class="text-muted">En attente</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-danger"><?php echo e($user->reservations()->cancelled()->count()); ?></h4>
                            <small class="text-muted">Annulées</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QR Code Section -->
        <div class="col-lg-4">
            <div class="card sticky-top border-0 shadow-lg" style="top: 20px;">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-qrcode me-2"></i>Mon QR Code Personnel
                    </h5>
                </div>
                <div class="card-body text-center p-4">
                    <div class="profile-qr-section">
                        <div id="qr-code-display" class="profile-qr-code">
                            <div class="text-muted">
                                <i class="fas fa-qrcode fa-4x mb-3 text-primary"></i>
                                <h6>Cliquez pour générer</h6>
                                <p class="small">Votre QR code d'authentification</p>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="button" class="btn btn-primary btn-lg" onclick="generateUserQRCode()" id="generate-qr-btn">
                            <i class="fas fa-qrcode me-2"></i>Générer mon QR Code
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="downloadQRCode()" style="display: none;" id="download-qr-btn">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="regenerateUserQRCode()" style="display: none;" id="regenerate-qr-btn">
                            <i class="fas fa-sync me-2"></i>Régénérer
                        </button>
                        <a href="<?php echo e(route('qr.login')); ?>" class="btn btn-outline-info">
                            <i class="fas fa-camera me-2"></i>Scanner un QR Code
                        </a>
                    </div>

                    <div class="alert alert-info mt-3" role="alert" style="display: none;" id="qr-instructions">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Comment utiliser :</strong><br>
                            1. Allez sur la page QR Code d'un autre appareil<br>
                            2. Cliquez "Démarrer le scan"<br>
                            3. Scannez ce code pour vous connecter
                        </small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Mes réservations
                        </a>
                        <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-outline-success">
                            <i class="fas fa-search me-2"></i>Réserver un local
                        </a>
                        <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-info">
                            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
let currentQRImage = null;

// Générer le QR code utilisateur
async function generateUserQRCode() {
    try {
        const generateBtn = document.getElementById('generate-qr-btn');
        if (!generateBtn) {
            console.error('Bouton de génération non trouvé');
            return;
        }

        const originalText = generateBtn.innerHTML;

        // Loading state
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Génération...';
        generateBtn.disabled = true;

        console.log('🔄 Génération du QR code...');

        // Vérifier le token CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            throw new Error('Token CSRF manquant');
        }

        const response = await fetch('/api/qr/generate-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Accept': 'application/json'
            }
        });

        console.log('📡 Réponse reçue:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Erreur HTTP:', response.status, errorText);
            throw new Error(`Erreur HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('📦 Données reçues:', data);

        if (data.success) {
            // Afficher le QR code
            displayQRCode(data.qr_image);
            currentQRImage = data.qr_image;

            // Afficher les boutons d'action
            const downloadBtn = document.getElementById('download-qr-btn');
            const regenerateBtn = document.getElementById('regenerate-qr-btn');
            const instructions = document.getElementById('qr-instructions');

            if (downloadBtn) downloadBtn.style.display = 'block';
            if (regenerateBtn) regenerateBtn.style.display = 'block';
            if (instructions) instructions.style.display = 'block';

            // Masquer le bouton de génération
            generateBtn.style.display = 'none';

            // Notification de succès
            showNotification('QR Code généré avec succès !', 'success');
            console.log('✅ QR Code généré avec succès');
        } else {
            console.error('❌ Erreur dans la réponse:', data);
            showNotification(data.error || 'Erreur lors de la génération du QR code', 'error');
        }
    } catch (error) {
        console.error('❌ Erreur complète:', error);
        showNotification(`Erreur: ${error.message}`, 'error');
    } finally {
        const generateBtn = document.getElementById('generate-qr-btn');
        if (generateBtn) {
            generateBtn.innerHTML = generateBtn.innerHTML.includes('Génération') ?
                '<i class="fas fa-qrcode me-2"></i>Générer mon QR Code' : generateBtn.innerHTML;
            generateBtn.disabled = false;
        }
    }
}

// Régénérer le QR code
async function regenerateUserQRCode() {
    if (confirm('Êtes-vous sûr de vouloir régénérer votre QR code ? L\'ancien code ne fonctionnera plus.')) {
        await generateUserQRCode();
    }
}

// Afficher le QR code dans l'interface
function displayQRCode(qrImage) {
    const qrDisplay = document.getElementById('qr-code-display');
    qrDisplay.innerHTML = `
        <img src="${qrImage}" alt="QR Code" class="img-fluid" style="max-width: 200px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
    `;
}

// Télécharger le QR code
function downloadQRCode() {
    if (currentQRImage) {
        const link = document.createElement('a');
        link.download = 'mon-qr-code-locaspace.png';
        link.href = currentQRImage;
        link.click();

        showNotification('QR Code téléchargé !', 'success');
    } else {
        showNotification('Aucun QR code à télécharger', 'error');
    }
}

// Afficher une notification
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Test simple de l'API
async function testQRAPI() {
    try {
        console.log('🧪 Test de l\'API QR...');

        const response = await fetch('/api/qr/generate-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        });

        console.log('📡 Status:', response.status);
        console.log('📡 Headers:', [...response.headers.entries()]);

        const text = await response.text();
        console.log('📦 Raw response:', text);

        try {
            const data = JSON.parse(text);
            console.log('📦 Parsed data:', data);
        } catch (e) {
            console.error('❌ JSON parse error:', e);
        }

    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

// Auto-générer le QR code au chargement si l'utilisateur en a déjà un
document.addEventListener('DOMContentLoaded', function() {
    console.log('Profile QR section loaded');

    // Ajouter un bouton de test en mode debug
    if (window.location.search.includes('debug=1')) {
        const testBtn = document.createElement('button');
        testBtn.className = 'btn btn-warning btn-sm mt-2';
        testBtn.innerHTML = '<i class="fas fa-bug me-1"></i>Test API';
        testBtn.onclick = testQRAPI;

        const qrSection = document.querySelector('.profile-qr-section');
        if (qrSection) {
            qrSection.appendChild(testBtn);
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/profile/show.blade.php ENDPATH**/ ?>