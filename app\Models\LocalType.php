<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocalType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_fr',
        'name_ar',
        'description',
        'icon',
        'color',
        'active',
        'sort_order',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    /**
     * Get locals of this type.
     */
    public function locals()
    {
        return $this->hasMany(Local::class, 'type', 'name');
    }

    /**
     * Scope to get only active types.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the display name based on locale.
     */
    public function getDisplayNameAttribute()
    {
        $locale = app()->getLocale();
        
        if ($locale === 'fr' && $this->name_fr) {
            return $this->name_fr;
        } elseif ($locale === 'ar' && $this->name_ar) {
            return $this->name_ar;
        }
        
        return $this->name;
    }

    /**
     * Get all active types for dropdown.
     */
    public static function getForDropdown()
    {
        return static::active()->ordered()->get()->pluck('display_name', 'name');
    }
}
