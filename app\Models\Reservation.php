<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'local_id',
        'date',
        'start_time',
        'end_time',
        'status',
        'selected_pricing_type',
        'duration_count',
        'duration_unit',
    ];

    protected function casts(): array
    {
        return [
            'date' => 'date',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
        ];
    }

    /**
     * Get the user that owns the reservation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the local that is reserved.
     */
    public function local(): BelongsTo
    {
        return $this->belongsTo(Local::class);
    }

    /**
     * Get the invoice for the reservation.
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    /**
     * Calculate the duration in hours.
     */
    public function getDurationInHours(): float
    {
        // Get date string
        $dateStr = $this->date instanceof \Carbon\Carbon ? $this->date->format('Y-m-d') : $this->date;

        // Get time strings
        $startTimeStr = $this->start_time instanceof \Carbon\Carbon ? $this->start_time->format('H:i:s') : $this->start_time;
        $endTimeStr = $this->end_time instanceof \Carbon\Carbon ? $this->end_time->format('H:i:s') : $this->end_time;

        // Parse times with the reservation date
        $start = \Carbon\Carbon::parse($dateStr . ' ' . $startTimeStr);
        $end = \Carbon\Carbon::parse($dateStr . ' ' . $endTimeStr);

        // If end time is before start time, assume it's next day
        if ($end->lt($start)) {
            $end->addDay();
        }

        return $start->diffInHours($end, false); // false = signed difference
    }

    /**
     * Calculate the total amount for this reservation.
     */
    public function calculateAmount(): float
    {
        $local = $this->local;
        $selectedPricingType = $this->selected_pricing_type ?? 'hourly';
        $durationCount = $this->duration_count ?? 1;
        $durationUnit = $this->duration_unit ?? 'hours';
        $amount = 0; // Initialize amount

        if ($local->pricing_type === 'flexible' && $selectedPricingType !== 'fixed') {
            // Use the user's selected pricing type and duration
            switch ($selectedPricingType) {
                case 'yearly':
                    if ($local->yearly_price > 0) {
                        $amount = $durationCount * $local->yearly_price;
                        break;
                    }
                    // Fall through to monthly if yearly not available
                case 'monthly':
                    if ($local->monthly_price > 0) {
                        $amount = $durationCount * $local->monthly_price;
                        break;
                    }
                    // Fall through to weekly if monthly not available
                case 'weekly':
                    if ($local->weekly_price > 0) {
                        $amount = $durationCount * $local->weekly_price;
                        break;
                    }
                    // Fall through to daily if weekly not available
                case 'daily':
                    if ($local->daily_price > 0) {
                        $amount = $durationCount * $local->daily_price;
                        break;
                    }
                    // Fall through to hourly if daily not available
                case 'hourly':
                default:
                    $amount = $durationCount * ($local->hourly_price ?? $local->price ?? 0);
                    break;
            }

            // If amount is still 0, use hourly pricing as final fallback
            if ($amount <= 0) {
                $amount = $durationCount * ($local->hourly_price ?? $local->price ?? 0);
            }
        } else {
            // Fixed pricing
            $amount = $local->price ?? 0;
        }

        return abs($amount); // S'assurer que le montant est toujours positif
    }

    /**
     * Scope for confirmed reservations.
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmée');
    }

    /**
     * Scope for pending reservations.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'en attente');
    }

    /**
     * Scope for cancelled reservations.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'annulée');
    }

    /**
     * Scope for reservations on a specific date.
     */
    public function scopeOnDate($query, $date)
    {
        return $query->where('date', $date);
    }
}
