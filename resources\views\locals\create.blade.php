@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.locals') }}">Locaux</a></li>
            <li class="breadcrumb-item active">Créer un nouveau local</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Créer un nouveau local
                    </h1>
                    <p class="text-muted">Ajoutez un nouveau local à votre plateforme</p>
                </div>
                <div>
                    <a href="{{ route('admin.locals') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations du local
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.locals.store') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Nom -->
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom du local <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Type -->
                        <div class="mb-3">
                            <label for="type" class="form-label">Type de local <span class="text-danger">*</span></label>
                            <div class="d-flex align-items-center">
                                <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                    <option value="">Sélectionnez un type</option>
                                    @foreach(\App\Models\LocalType::active()->ordered()->get() as $localType)
                                        <option value="{{ $localType->name }}"
                                                {{ old('type') == $localType->name ? 'selected' : '' }}
                                                data-icon="{{ $localType->icon }}"
                                                data-color="{{ $localType->color }}">
                                            {{ $localType->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <a href="{{ route('admin.local-types.create') }}" class="btn btn-outline-primary btn-sm ms-2" title="Ajouter un nouveau type">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Vous pouvez ajouter de nouveaux types de locaux en cliquant sur le bouton +
                            </small>
                        </div>

                        <!-- Localisation -->
                        <div class="mb-3">
                            <label for="location" class="form-label">Localisation <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror"
                                   id="location" name="location" value="{{ old('location') }}" required>
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Configuration des prix -->
                        <div class="mb-4">
                            <label class="form-label">Configuration des prix <span class="text-danger">*</span></label>

                            <!-- Type de tarification -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="pricing_type" id="fixed_pricing" value="fixed" {{ old('pricing_type', 'fixed') == 'fixed' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="fixed_pricing">
                                        <strong>Prix fixe</strong> (par réservation)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="pricing_type" id="flexible_pricing" value="flexible" {{ old('pricing_type') == 'flexible' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="flexible_pricing">
                                        <strong>Prix flexible</strong> (horaire, journalier, hebdomadaire, mensuel, annuel)
                                    </label>
                                </div>
                            </div>

                            <!-- Section prix fixe -->
                            <div id="fixed_price_section" class="pricing-section">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Prix fixe (MAD) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('price') is-invalid @enderror"
                                               id="price" name="price" value="{{ old('price') }}" min="0" step="0.01">
                                        <span class="input-group-text">MAD</span>
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Section prix flexible -->
                            <div id="flexible_price_section" class="pricing-section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hourly_price" class="form-label">Prix horaire (MAD) <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('hourly_price') is-invalid @enderror"
                                                       id="hourly_price" name="hourly_price" value="{{ old('hourly_price') }}" min="0" step="0.01">
                                                <span class="input-group-text">MAD/h</span>
                                            </div>
                                            @error('hourly_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="daily_price" class="form-label">Prix journalier (MAD)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('daily_price') is-invalid @enderror"
                                                       id="daily_price" name="daily_price" value="{{ old('daily_price') }}" min="0" step="0.01">
                                                <span class="input-group-text">MAD/jour</span>
                                            </div>
                                            @error('daily_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="weekly_price" class="form-label">Prix hebdomadaire (MAD)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('weekly_price') is-invalid @enderror"
                                                       id="weekly_price" name="weekly_price" value="{{ old('weekly_price') }}" min="0" step="0.01">
                                                <span class="input-group-text">MAD/sem</span>
                                            </div>
                                            @error('weekly_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="monthly_price" class="form-label">Prix mensuel (MAD)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('monthly_price') is-invalid @enderror"
                                                       id="monthly_price" name="monthly_price" value="{{ old('monthly_price') }}" min="0" step="0.01">
                                                <span class="input-group-text">MAD/mois</span>
                                            </div>
                                            @error('monthly_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="yearly_price" class="form-label">Prix annuel (MAD)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control @error('yearly_price') is-invalid @enderror"
                                                       id="yearly_price" name="yearly_price" value="{{ old('yearly_price') }}" min="0" step="0.01">
                                                <span class="input-group-text">MAD/an</span>
                                            </div>
                                            @error('yearly_price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration de disponibilité -->
                        <div class="mb-4">
                            <label class="form-label">Configuration de disponibilité</label>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="minimum_rental_hours" class="form-label">Durée minimale de location (heures)</label>
                                        <input type="number" class="form-control @error('minimum_rental_hours') is-invalid @enderror"
                                               id="minimum_rental_hours" name="minimum_rental_hours" value="{{ old('minimum_rental_hours', 1) }}" min="1">
                                        @error('minimum_rental_hours')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maximum_rental_hours" class="form-label">Durée maximale de location (heures)</label>
                                        <input type="number" class="form-control @error('maximum_rental_hours') is-invalid @enderror"
                                               id="maximum_rental_hours" name="maximum_rental_hours" value="{{ old('maximum_rental_hours') }}" min="1">
                                        <small class="form-text text-muted">Laisser vide pour aucune limite</small>
                                        @error('maximum_rental_hours')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="available_24_7" name="available_24_7" value="1" {{ old('available_24_7') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="available_24_7">
                                        Disponible 24h/24 et 7j/7
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="approval_required" class="form-label">Approbation des réservations</label>
                                <select class="form-control @error('approval_required') is-invalid @enderror" id="approval_required" name="approval_required">
                                    <option value="manual" {{ old('approval_required', 'manual') == 'manual' ? 'selected' : '' }}>Approbation manuelle requise</option>
                                    <option value="automatic" {{ old('approval_required') == 'automatic' ? 'selected' : '' }}>Approbation automatique</option>
                                </select>
                                @error('approval_required')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Capacité -->
                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacité maximale</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1">
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Équipements -->
                        <div class="mb-3">
                            <label for="equipment" class="form-label">Équipements disponibles</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="wifi" id="equipment_wifi" {{ in_array('wifi', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_wifi">
                                            <i class="fas fa-wifi me-2"></i>WiFi
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="projecteur" id="equipment_projecteur" {{ in_array('projecteur', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_projecteur">
                                            <i class="fas fa-video me-2"></i>Projecteur
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="climatisation" id="equipment_climatisation" {{ in_array('climatisation', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_climatisation">
                                            <i class="fas fa-snowflake me-2"></i>Climatisation
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="parking" id="equipment_parking" {{ in_array('parking', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_parking">
                                            <i class="fas fa-car me-2"></i>Parking
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="son" id="equipment_son" {{ in_array('son', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_son">
                                            <i class="fas fa-volume-up me-2"></i>Système audio
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="eclairage" id="equipment_eclairage" {{ in_array('eclairage', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_eclairage">
                                            <i class="fas fa-lightbulb me-2"></i>Éclairage professionnel
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="vestiaires" id="equipment_vestiaires" {{ in_array('vestiaires', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_vestiaires">
                                            <i class="fas fa-tshirt me-2"></i>Vestiaires
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="cuisine" id="equipment_cuisine" {{ in_array('cuisine', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_cuisine">
                                            <i class="fas fa-utensils me-2"></i>Cuisine équipée
                                        </label>
                                    </div>
                                </div>
                            </div>
                            @error('equipment')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Sélectionnez tous les équipements disponibles dans ce local</small>
                        </div>

                        <!-- Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Image du local</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror"
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF. Taille max: 2MB</small>
                        </div>

                        <!-- Disponibilité -->
                        <div class="mb-3">
                            <label class="form-label">Disponibilité</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available"
                                       value="1" {{ old('is_available', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_available">
                                    Local disponible pour les réservations
                                </label>
                            </div>
                        </div>

                        <!-- Boutons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.locals') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Créer le local
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Aide -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Aide
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Conseils pour créer un local :</h6>
                    <ul class="small">
                        <li>Utilisez un nom descriptif et accrocheur</li>
                        <li>Décrivez clairement les caractéristiques</li>
                        <li>Ajoutez une image de qualité</li>
                        <li>Indiquez tous les équipements disponibles</li>
                        <li>Fixez un prix compétitif</li>
                    </ul>
                </div>
            </div>

            <!-- Aperçu du type -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>Types de locaux disponibles
                    </h6>
                </div>
                <div class="card-body">
                    @foreach(\App\Models\LocalType::active()->ordered()->get() as $localType)
                        <div class="mb-2">
                            <i class="{{ $localType->icon }} me-2" style="color: {{ $localType->color }}"></i>
                            <strong>{{ $localType->display_name }} :</strong>
                            {{ $localType->description ?: 'Type de local personnalisé' }}
                        </div>
                    @endforeach

                    @if(\App\Models\LocalType::active()->count() == 0)
                        <div class="text-muted text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            Aucun type de local disponible.
                            <a href="{{ route('admin.local-types.create') }}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus me-1"></i>Créer le premier type
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Gestion des types -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Gestion des types
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.local-types.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-2"></i>Voir tous les types
                        </a>
                        <a href="{{ route('admin.local-types.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-2"></i>Ajouter un nouveau type
                        </a>
                    </div>
                    <small class="text-muted mt-2 d-block">
                        Gérez les types de locaux pour personnaliser votre plateforme
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion du toggle des sections de prix
    const fixedRadio = document.getElementById('fixed_pricing');
    const flexibleRadio = document.getElementById('flexible_pricing');
    const fixedSection = document.getElementById('fixed_price_section');
    const flexibleSection = document.getElementById('flexible_price_section');

    function togglePricingSections() {
        if (fixedRadio.checked) {
            fixedSection.style.display = 'block';
            flexibleSection.style.display = 'none';
        } else {
            fixedSection.style.display = 'none';
            flexibleSection.style.display = 'block';
        }
    }

    fixedRadio.addEventListener('change', togglePricingSections);
    flexibleRadio.addEventListener('change', togglePricingSections);

    // Initialiser au chargement de la page
    togglePricingSections();

    // Aperçu de l'image
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Créer un aperçu si nécessaire
                console.log('Image sélectionnée:', file.name);
            };
            reader.readAsDataURL(file);
        }
    });

    // Validation du formulaire
    document.querySelector('form').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const description = document.getElementById('description').value.trim();
        const type = document.getElementById('type').value;
        const location = document.getElementById('location').value.trim();

        // Validation des prix selon le type
        const pricingType = document.querySelector('input[name="pricing_type"]:checked').value;
        let priceValid = false;

        if (pricingType === 'fixed') {
            const price = document.getElementById('price').value;
            priceValid = price && parseFloat(price) > 0;
        } else {
            const hourlyPrice = document.getElementById('hourly_price').value;
            priceValid = hourlyPrice && parseFloat(hourlyPrice) > 0;
        }

        if (!name || !description || !type || !location || !priceValid) {
            e.preventDefault();
            alert('Veuillez remplir tous les champs obligatoires.');
            return false;
        }
    });
});
</script>
@endsection
