<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.index')); ?>">Locaux</a></li>
            <li class="breadcrumb-item active"><?php echo e($local->name); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Local Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <?php if($local->type === 'sport'): ?>
                                <i class="fas fa-futbol text-success me-2"></i>
                            <?php elseif($local->type === 'conference'): ?>
                                <i class="fas fa-presentation-screen text-primary me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-glass-cheers text-warning me-2"></i>
                            <?php endif; ?>
                            <span class="badge bg-secondary"><?php echo e(ucfirst($local->type)); ?></span>
                        </div>
                        <div class="text-end">
                            <span class="h4 text-success mb-0"><?php echo e(abs($local->price)); ?> MAD</span>
                            <small class="text-muted">/heure</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h1 class="h3 mb-3"><?php echo e($local->name); ?></h1>

                    <?php if($local->image): ?>
                        <div class="mb-4">
                            <img src="<?php echo e(asset('storage/' . $local->image)); ?>"
                                 alt="<?php echo e($local->name); ?>"
                                 class="img-fluid rounded shadow-sm w-100"
                                 style="max-height: 400px; object-fit: cover;">
                        </div>
                    <?php endif; ?>

                    <?php if($local->description): ?>
                        <div class="mb-4">
                            <h5><i class="fas fa-align-left text-primary me-2"></i>Description</h5>
                            <p class="text-muted"><?php echo e($local->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle text-primary me-2"></i>Informations générales</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                    <strong>Localisation :</strong> <?php echo e($local->location); ?>

                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-users text-muted me-2"></i>
                                    <strong>Capacité :</strong> <?php echo e($local->capacity); ?> personnes
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-tag text-muted me-2"></i>
                                    <strong>Type :</strong> <?php echo e(ucfirst($local->type)); ?>

                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-coins text-muted me-2"></i>
                                    <strong>Prix :</strong> <?php echo e(abs($local->price)); ?> MAD par heure
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-cogs text-primary me-2"></i>Équipements disponibles</h5>
                            <?php if($local->equipment && count($local->equipment) > 0): ?>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark border">
                                            <?php if($equipment === 'wifi'): ?>
                                                <i class="fas fa-wifi me-1"></i>WiFi
                                            <?php elseif($equipment === 'projecteur'): ?>
                                                <i class="fas fa-video me-1"></i>Projecteur
                                            <?php elseif($equipment === 'climatisation'): ?>
                                                <i class="fas fa-snowflake me-1"></i>Climatisation
                                            <?php elseif($equipment === 'parking'): ?>
                                                <i class="fas fa-parking me-1"></i>Parking
                                            <?php elseif($equipment === 'cuisine'): ?>
                                                <i class="fas fa-utensils me-1"></i>Cuisine
                                            <?php else: ?>
                                                <i class="fas fa-check me-1"></i><?php echo e($equipment); ?>

                                            <?php endif; ?>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">Aucun équipement spécifique mentionné</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Calendar Placeholder -->
                    <div class="card bg-light">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>Disponibilités
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="calendar-container">
                                <p class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    Chargement du calendrier des disponibilités...
                                </p>
                                <!-- Le calendrier sera intégré ici avec JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Reservation Card -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Réserver ce local
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="text-center mb-3">
                            <div class="h4 text-success"><?php echo e(abs($local->price)); ?> MAD <small class="text-muted">/heure</small></div>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('reservations.create', $local)); ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-calendar-plus me-2"></i>Réserver maintenant
                            </a>
                            <button type="button" class="btn btn-outline-primary" onclick="checkAvailability()">
                                <i class="fas fa-search me-2"></i>Vérifier disponibilité
                            </button>
                        </div>

                        <hr>

                        <div class="small text-muted">
                            <h6>Informations importantes :</h6>
                            <ul class="mb-0">
                                <li>Paiement sécurisé en ligne</li>
                                <li>Confirmation immédiate</li>
                                <li>Facture PDF automatique</li>
                                <li>Annulation possible</li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <p class="mb-3">Connectez-vous pour réserver ce local</p>
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                                </a>
                                <a href="<?php echo e(route('register')); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>S'inscrire
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Une question sur ce local ? Contactez notre équipe.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Nous contacter
                        </button>
                    </div>
                </div>
            </div>

            <!-- Similar Locals -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-thumbs-up me-2"></i>Locaux similaires
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small">
                        Découvrez d'autres locaux du même type dans la région.
                    </p>
                    <div class="d-grid">
                        <a href="<?php echo e(route('locals.index', ['type' => $local->type])); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-2"></i>Voir les locaux <?php echo e($local->type); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function checkAvailability() {
    // Placeholder for availability check
    alert('Fonctionnalité de vérification des disponibilités à implémenter');
}

// Placeholder for calendar integration
document.addEventListener('DOMContentLoaded', function() {
    // Here you would integrate a calendar library like FullCalendar
    setTimeout(function() {
        document.getElementById('calendar-container').innerHTML = `
            <div class="text-center">
                <p class="text-muted">Calendrier interactif à implémenter</p>
                <small>Utilisez le bouton "Réserver maintenant" pour accéder au formulaire de réservation</small>
            </div>
        `;
    }, 1000);
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/locals/show.blade.php ENDPATH**/ ?>