<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.index')); ?>">Locaux</a></li>
            <li class="breadcrumb-item active"><?php echo e($local->name); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Local Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <?php if($local->type === 'sport'): ?>
                                <i class="fas fa-futbol text-success me-2"></i>
                            <?php elseif($local->type === 'conference'): ?>
                                <i class="fas fa-presentation-screen text-primary me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-glass-cheers text-warning me-2"></i>
                            <?php endif; ?>
                            <span class="badge bg-secondary"><?php echo e(ucfirst($local->type)); ?></span>
                        </div>
                        <div class="text-end">
                            <span class="h4 text-success mb-0"><?php echo e(abs($local->price)); ?> MAD</span>
                            <small class="text-muted">/heure</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h1 class="h3 mb-3"><?php echo e($local->name); ?></h1>

                    <?php if($local->image): ?>
                        <div class="mb-4">
                            <img src="<?php echo e(asset('storage/' . $local->image)); ?>"
                                 alt="<?php echo e($local->name); ?>"
                                 class="img-fluid rounded shadow-sm w-100"
                                 style="max-height: 400px; object-fit: cover;">
                        </div>
                    <?php endif; ?>

                    <?php if($local->description): ?>
                        <div class="mb-4">
                            <h5><i class="fas fa-align-left text-primary me-2"></i>Description</h5>
                            <p class="text-muted"><?php echo e($local->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle text-primary me-2"></i>Informations générales</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                    <strong>Localisation :</strong> <?php echo e($local->location); ?>

                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-users text-muted me-2"></i>
                                    <strong>Capacité :</strong> <?php echo e($local->capacity); ?> personnes
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-tag text-muted me-2"></i>
                                    <strong>Type :</strong> <?php echo e(ucfirst($local->type)); ?>

                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-coins text-muted me-2"></i>
                                    <strong>Prix :</strong> <?php echo e(abs($local->price)); ?> MAD par heure
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-cogs text-primary me-2"></i>Équipements disponibles</h5>
                            <?php if($local->equipment && count($local->equipment) > 0): ?>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark border">
                                            <?php if($equipment === 'wifi'): ?>
                                                <i class="fas fa-wifi me-1"></i>WiFi
                                            <?php elseif($equipment === 'projecteur'): ?>
                                                <i class="fas fa-video me-1"></i>Projecteur
                                            <?php elseif($equipment === 'climatisation'): ?>
                                                <i class="fas fa-snowflake me-1"></i>Climatisation
                                            <?php elseif($equipment === 'parking'): ?>
                                                <i class="fas fa-parking me-1"></i>Parking
                                            <?php elseif($equipment === 'cuisine'): ?>
                                                <i class="fas fa-utensils me-1"></i>Cuisine
                                            <?php else: ?>
                                                <i class="fas fa-check me-1"></i><?php echo e($equipment); ?>

                                            <?php endif; ?>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">Aucun équipement spécifique mentionné</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Interactive Calendar -->
                    <div class="card bg-light">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendrier des disponibilités
                                </h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="prevMonth">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="todayBtn">
                                        Aujourd'hui
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="nextMonth">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <span class="badge bg-primary me-2" id="currentMonth"><?php echo e(now()->format('F Y')); ?></span>
                                <small class="text-muted">Cliquez sur une date pour voir les détails</small>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="local-calendar" class="local-calendar-container"></div>
                        </div>
                        <div class="card-footer bg-white">
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="calendar-legend-dot bg-success me-2"></div>
                                        <small>Disponible</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="calendar-legend-dot bg-warning me-2"></div>
                                        <small>Partiellement réservé</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="calendar-legend-dot bg-danger me-2"></div>
                                        <small>Complet</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="calendar-legend-dot bg-secondary me-2"></div>
                                        <small>Passé</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Date Details -->
                    <div class="card mt-4" id="selectedDateCard" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-day me-2"></i>
                                <span id="selectedDateTitle">Détails de la date</span>
                            </h6>
                        </div>
                        <div class="card-body" id="selectedDateDetails">
                            <!-- Dynamic content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Reservation Card -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Réserver ce local
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- Pricing Display -->
                        <div class="text-center mb-3">
                            <?php if($local->pricing_type === 'flexible'): ?>
                                <div class="h4 text-success"><?php echo e(abs($local->hourly_price ?? $local->price)); ?> MAD <small class="text-muted">/heure</small></div>
                                <?php if($local->daily_price): ?>
                                    <div class="small text-muted"><?php echo e($local->daily_price); ?> MAD/jour</div>
                                <?php endif; ?>
                                <?php if($local->weekly_price): ?>
                                    <div class="small text-muted"><?php echo e($local->weekly_price); ?> MAD/semaine</div>
                                <?php endif; ?>
                                <?php if($local->monthly_price): ?>
                                    <div class="small text-muted"><?php echo e($local->monthly_price); ?> MAD/mois</div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="h4 text-success"><?php echo e(abs($local->price)); ?> MAD <small class="text-muted">/heure</small></div>
                            <?php endif; ?>
                        </div>

                        <!-- Social Proof -->
                        <?php if($totalReservations > 0): ?>
                        <div class="alert alert-info mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users text-info me-2"></i>
                                <div>
                                    <strong><?php echo e($totalReservations); ?></strong> réservations confirmées
                                    <?php if($recentReservations > 0): ?>
                                        <br><small><?php echo e($recentReservations); ?> ce mois-ci</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('reservations.create', $local)); ?>" class="btn btn-primary btn-lg" id="reserveBtn">
                                <i class="fas fa-calendar-plus me-2"></i>Réserver maintenant
                            </a>
                            <button type="button" class="btn btn-outline-primary" id="quickCheckBtn">
                                <i class="fas fa-search me-2"></i>Vérifier disponibilité
                            </button>
                        </div>

                        <!-- Quick Availability Check -->
                        <div class="mt-3" id="quickAvailabilityCheck" style="display: none;">
                            <div class="card border-primary">
                                <div class="card-body p-3">
                                    <h6 class="card-title">Vérification rapide</h6>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <input type="date" class="form-control form-control-sm" id="quickDate" min="<?php echo e(now()->format('Y-m-d')); ?>">
                                        </div>
                                        <div class="col-6">
                                            <button type="button" class="btn btn-primary btn-sm w-100" onclick="quickAvailabilityCheck()">
                                                <i class="fas fa-search me-1"></i>Vérifier
                                            </button>
                                        </div>
                                    </div>
                                    <div id="quickAvailabilityResult" class="mt-2"></div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="small text-muted">
                            <h6>Informations importantes :</h6>
                            <ul class="mb-0">
                                <li>Paiement sécurisé en ligne</li>
                                <li>Confirmation immédiate</li>
                                <li>Facture PDF automatique</li>
                                <li>Annulation possible</li>
                                <?php if($local->approval_required === 'manual'): ?>
                                    <li class="text-warning">Approbation manuelle requise</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <p class="mb-3">Connectez-vous pour réserver ce local</p>
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                                </a>
                                <a href="<?php echo e(route('register')); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>S'inscrire
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Une question sur ce local ? Contactez notre équipe.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Nous contacter
                        </button>
                    </div>
                </div>
            </div>

            <!-- Similar Locals -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-thumbs-up me-2"></i>Locaux similaires
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small">
                        Découvrez d'autres locaux du même type dans la région.
                    </p>
                    <div class="d-grid">
                        <a href="<?php echo e(route('locals.index', ['type' => $local->type])); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-2"></i>Voir les locaux <?php echo e($local->type); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
/* Local Calendar Specific Styles */
.local-calendar-container {
    background: white;
    border-radius: 0;
}

.local-calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
}

.local-calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.local-calendar-day-header {
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    background: #f8f9fa;
}

.local-calendar-day {
    background: white;
    min-height: 70px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border: 2px solid transparent;
}

.local-calendar-day:hover {
    background: #f8f9ff;
    transform: scale(1.02);
    border-color: #007bff;
    z-index: 2;
}

.local-calendar-day.selected {
    background: #e7f3ff;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.local-calendar-day.other-month {
    background: #f8f9fa;
    color: #adb5bd;
}

.local-calendar-day.past {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.local-calendar-day.today {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-weight: bold;
}

.local-calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.local-calendar-day-status {
    position: absolute;
    bottom: 0.25rem;
    left: 0.25rem;
    right: 0.25rem;
    height: 4px;
    border-radius: 2px;
    background: #28a745;
}

.local-calendar-day-status.partial {
    background: #ffc107;
}

.local-calendar-day-status.full {
    background: #dc3545;
}

.local-calendar-day-status.past {
    background: #6c757d;
}

.local-calendar-reservations {
    font-size: 0.6rem;
    color: #6c757d;
    margin-top: 0.125rem;
}

.calendar-legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.reservation-time-slot {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #007bff;
    transition: all 0.2s ease;
}

.reservation-time-slot:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.reservation-time-slot.confirmed {
    border-left-color: #28a745;
}

.reservation-time-slot.pending {
    border-left-color: #ffc107;
}

.local-calendar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: #6c757d;
}

.local-calendar-fade-in {
    animation: localCalendarFadeIn 0.3s ease-out;
}

@keyframes localCalendarFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .local-calendar-day {
        min-height: 50px;
        padding: 0.25rem;
    }

    .local-calendar-day-header {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .local-calendar-reservations {
        display: none;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
class LocalCalendar {
    constructor(localId) {
        this.localId = localId;
        this.currentDate = new Date();
        this.selectedDate = null;
        this.reservationsData = {};
        this.monthNames = [
            'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
            'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ];
        this.dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

        this.init();
    }

    init() {
        this.bindEvents();
        this.renderCalendar();
        this.loadReservationsData();
    }

    bindEvents() {
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.renderCalendar();
            this.loadReservationsData();
        });

        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.renderCalendar();
            this.loadReservationsData();
        });

        document.getElementById('todayBtn').addEventListener('click', () => {
            this.currentDate = new Date();
            this.renderCalendar();
            this.loadReservationsData();
        });

        // Quick availability check
        document.getElementById('quickCheckBtn').addEventListener('click', () => {
            const quickCheck = document.getElementById('quickAvailabilityCheck');
            quickCheck.style.display = quickCheck.style.display === 'none' ? 'block' : 'none';
        });
    }

    renderCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        // Update month display
        document.getElementById('currentMonth').textContent =
            `${this.monthNames[month]} ${year}`;

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Create calendar HTML
        let calendarHTML = '<div class="local-calendar-header">';

        // Day headers
        this.dayNames.forEach(day => {
            calendarHTML += `<div class="local-calendar-day-header">${day}</div>`;
        });

        calendarHTML += '</div><div class="local-calendar-grid">';

        // Previous month's trailing days
        const prevMonth = new Date(year, month - 1, 0);
        const prevMonthDays = prevMonth.getDate();

        for (let i = startingDayOfWeek - 1; i >= 0; i--) {
            const day = prevMonthDays - i;
            calendarHTML += `
                <div class="local-calendar-day other-month">
                    <div class="local-calendar-day-number">${day}</div>
                </div>
            `;
        }

        // Current month days
        const today = new Date();
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDay = new Date(year, month, day);
            const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

            let classes = ['local-calendar-day'];

            // Check if it's today
            if (currentDay.toDateString() === today.toDateString()) {
                classes.push('today');
            }

            // Check if it's in the past
            if (currentDay < today && currentDay.toDateString() !== today.toDateString()) {
                classes.push('past');
            }

            calendarHTML += `
                <div class="${classes.join(' ')}" data-date="${dateString}" onclick="localCalendar.selectDate('${dateString}')">
                    <div class="local-calendar-day-number">${day}</div>
                    <div class="local-calendar-reservations" id="local-reservations-${dateString}"></div>
                    <div class="local-calendar-day-status" id="local-status-${dateString}"></div>
                </div>
            `;
        }

        // Next month's leading days
        const remainingCells = 42 - (startingDayOfWeek + daysInMonth);
        for (let day = 1; day <= remainingCells && remainingCells < 7; day++) {
            calendarHTML += `
                <div class="local-calendar-day other-month">
                    <div class="local-calendar-day-number">${day}</div>
                </div>
            `;
        }

        calendarHTML += '</div>';

        document.getElementById('local-calendar').innerHTML = calendarHTML;
        document.getElementById('local-calendar').classList.add('local-calendar-fade-in');
    }

    async loadReservationsData() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth() + 1;

        try {
            // Show loading state
            const loadingHTML = `
                <div class="local-calendar-loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <span class="ms-2">Chargement...</span>
                </div>
            `;
            document.getElementById('local-calendar').innerHTML = loadingHTML;

            // Fetch reservations data for this local
            const response = await fetch(`/api/locals/${this.localId}/calendar?year=${year}&month=${month}`);
            const data = await response.json();

            this.reservationsData = data.reservations || {};

            // Re-render calendar with data
            this.renderCalendar();
            this.updateCalendarWithReservations();

        } catch (error) {
            console.error('Error loading reservations data:', error);
            // Render calendar anyway with empty data
            this.renderCalendar();
        }
    }

    updateCalendarWithReservations() {
        Object.keys(this.reservationsData).forEach(date => {
            const reservations = this.reservationsData[date];
            const reservationsElement = document.getElementById(`local-reservations-${date}`);
            const statusElement = document.getElementById(`local-status-${date}`);

            if (reservationsElement && statusElement) {
                // Update reservations count
                if (reservations.length > 0) {
                    reservationsElement.textContent = `${reservations.length} rés.`;
                }

                // Update status indicator based on time coverage
                const totalHours = 24; // Assuming 24-hour availability
                const reservedHours = reservations.reduce((total, res) => total + res.duration, 0);

                if (reservedHours === 0) {
                    statusElement.className = 'local-calendar-day-status';
                } else if (reservedHours < totalHours * 0.5) {
                    statusElement.className = 'local-calendar-day-status partial';
                } else {
                    statusElement.className = 'local-calendar-day-status full';
                }
            }
        });
    }

    selectDate(dateString) {
        // Remove previous selection
        document.querySelectorAll('.local-calendar-day.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to clicked date
        const dayElement = document.querySelector(`[data-date="${dateString}"]`);
        if (dayElement && !dayElement.classList.contains('past')) {
            dayElement.classList.add('selected');
            this.selectedDate = dateString;
            this.showDateDetails(dateString);
        }
    }

    showDateDetails(dateString) {
        const reservations = this.reservationsData[dateString] || [];
        const date = new Date(dateString);
        const formattedDate = date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        document.getElementById('selectedDateTitle').textContent = formattedDate;

        let detailsHTML = '';

        if (reservations.length === 0) {
            detailsHTML = `
                <div class="text-center">
                    <i class="fas fa-calendar-plus fa-2x text-success mb-2 d-block"></i>
                    <p class="text-success mb-2">Local entièrement disponible</p>
                    <p class="text-muted small">Aucune réservation pour cette date</p>
                    <a href="<?php echo e(route('reservations.create', $local)); ?>?date=${dateString}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>Réserver cette date
                    </a>
                </div>
            `;
        } else {
            detailsHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted small">Créneaux réservés</span>
                        <span class="badge bg-primary">${reservations.length}</span>
                    </div>
                </div>
            `;

            reservations.forEach(reservation => {
                const statusClass = reservation.status === 'confirmée' ? 'confirmed' : 'pending';

                detailsHTML += `
                    <div class="reservation-time-slot ${statusClass}">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <strong>${reservation.start_time} - ${reservation.end_time}</strong>
                            <span class="badge bg-${statusClass === 'confirmed' ? 'success' : 'warning'} ms-2">
                                ${reservation.status}
                            </span>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            ${reservation.duration}h de réservation
                        </div>
                    </div>
                `;
            });

            detailsHTML += `
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('reservations.create', $local)); ?>?date=${dateString}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-plus me-1"></i>Réserver un autre créneau
                    </a>
                </div>
            `;
        }

        document.getElementById('selectedDateDetails').innerHTML = detailsHTML;
        document.getElementById('selectedDateCard').style.display = 'block';

        // Scroll to details
        document.getElementById('selectedDateCard').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }
}

// Quick availability check function
async function quickAvailabilityCheck() {
    const date = document.getElementById('quickDate').value;
    const resultDiv = document.getElementById('quickAvailabilityResult');

    if (!date) {
        resultDiv.innerHTML = '<div class="alert alert-warning alert-sm">Veuillez sélectionner une date</div>';
        return;
    }

    resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Vérification...</div>';

    try {
        const response = await fetch(`/api/locals/<?php echo e($local->id); ?>/check-availability`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: date,
                start_time: '09:00',
                end_time: '10:00'
            })
        });

        const data = await response.json();

        if (data.available) {
            resultDiv.innerHTML = `
                <div class="alert alert-success alert-sm">
                    <i class="fas fa-check me-1"></i>Disponible !
                    <a href="<?php echo e(route('reservations.create', $local)); ?>?date=${date}" class="alert-link">Réserver</a>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-warning alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>${data.message || 'Non disponible'}
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger alert-sm">Erreur lors de la vérification</div>';
    }
}

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.localCalendar = new LocalCalendar(<?php echo e($local->id); ?>);
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/locals/show.blade.php ENDPATH**/ ?>