@extends('layouts.app')

@section('content')

    <!-- Section Hero -->
    <section class="position-relative overflow-hidden"
        style="background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #004085 100%); min-height: 100vh;">
        <!-- Background Animation -->
        <div class="position-absolute w-100 h-100" style="opacity: 0.1;">
            <div class="position-absolute" style="top: 10%; left: 10%; animation: float 6s ease-in-out infinite;">
                <i class="fas fa-building fa-3x text-white"></i>
            </div>
            <div class="position-absolute" style="top: 20%; right: 15%; animation: float 8s ease-in-out infinite reverse;">
                <i class="fas fa-futbol fa-2x text-white"></i>
            </div>
            <div class="position-absolute" style="bottom: 30%; left: 20%; animation: float 7s ease-in-out infinite;">
                <i class="fas fa-glass-cheers fa-2x text-white"></i>
            </div>
        </div>

        <div class="container h-100 d-flex align-items-center">
            <div class="row w-100 align-items-center">
                <div class="col-lg-6 fade-in-left">
                    <div class="hero-content text-white">
                        <h1 class="display-3 fw-bold mb-4">
                            Réservez vos espaces en <span class="text-warning">quelques clics</span>
                        </h1>
                        <p class="lead mb-4 text-white-50">
                            Découvrez {{ $stats['total_locals'] }} locaux disponibles avec {{ $stats['total_reservations'] }} réservations confirmées.
                            Rejoignez {{ $stats['total_users'] }} utilisateurs satisfaits !
                        </p>
                        <div class="d-flex flex-column flex-md-row gap-3 mb-4">
                            <a href="{{ route('locals.index') }}" class="btn btn-warning btn-lg px-4 py-3">
                                <i class="fas fa-search me-2"></i>Découvrir nos locaux
                            </a>
                            @guest
                                <a href="{{ route('register') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                    <i class="fas fa-user-plus me-2"></i>S'inscrire gratuitement
                                </a>
                            @endguest
                        </div>

                        <!-- Quick Stats -->
                        <div class="row text-center mt-5">
                            <div class="col-4">
                                <h3 class="text-warning fw-bold">50+</h3>
                                <small class="text-white-50">Locaux</small>
                            </div>
                            <div class="col-4">
                                <h3 class="text-warning fw-bold">1000+</h3>
                                <small class="text-white-50">Réservations</small>
                            </div>
                            <div class="col-4">
                                <h3 class="text-warning fw-bold">24/7</h3>
                                <small class="text-white-50">Support</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 fade-in-right">
                    <div class="text-center position-relative">

                        <div class="hero-logo-container text-center" style="backdrop-filter: blur(30px);
        background: rgb(255 255 255 / 27%);
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 0 20px 0px #0000005c;">

                            <div style="background: #ffffff; padding: 10px; border-radius: 15px; display: inline-block; ">
                                <img src="{{ asset('images/logo.png') }}" alt="LocaSpace Logo"
                                    style="height: 100px; width: auto;">
                            </div>

                            <h2 class="text-white fw-bold mb-1 mt-3">LocaSpace</h2>
                            <p class="text-white-50 mb-0">Votre plateforme de réservation</p>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Types de locaux -->
    <section id="locaux" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Types de locaux disponibles</h2>
                <p class="lead text-muted">Choisissez le type d'espace qui correspond à vos besoins</p>
            </div>
            <div class="row g-4">
                @foreach($localTypes as $typeKey => $type)
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm border-0 hover-lift">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="{{ $type['icon'] }} text-{{ $type['color'] }}" style="font-size: 3rem;"></i>
                            </div>
                            <h3 class="card-title h4 fw-bold">{{ $type['name'] }}</h3>
                            <p class="card-text text-muted">{{ $type['description'] }}</p>

                            <!-- Statistics -->
                            <div class="mb-3">
                                <span class="badge bg-{{ $type['color'] }} bg-opacity-10 text-{{ $type['color'] }} px-3 py-2">
                                    <i class="fas fa-building me-1"></i>{{ $type['count'] }} locaux disponibles
                                </span>
                            </div>

                            <!-- Latest locals preview -->
                            @if($type['latest']->count() > 0)
                            <div class="mb-3">
                                <small class="text-muted d-block mb-2">Derniers ajouts :</small>
                                <div class="d-flex flex-wrap gap-1 justify-content-center">
                                    @foreach($type['latest']->take(2) as $local)
                                    <span class="badge bg-light text-dark small">{{ $local->name }}</span>
                                    @endforeach
                                    @if($type['latest']->count() > 2)
                                    <span class="badge bg-secondary small">+{{ $type['latest']->count() - 2 }}</span>
                                    @endif
                                </div>
                            </div>
                            @endif

                            <a href="{{ route('locals.index', ['type' => $typeKey]) }}" class="btn btn-outline-{{ $type['color'] }}">
                                Voir les locaux <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Section Statistiques -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">LocaSpace en chiffres</h2>
                <p class="lead text-muted">La confiance de nos utilisateurs en quelques statistiques</p>
            </div>
            <div class="row g-4">
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-building text-primary" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-primary mb-1">{{ $stats['total_locals'] }}</h3>
                            <p class="text-muted mb-0">Locaux disponibles</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-calendar-check text-success" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-success mb-1">{{ $stats['total_reservations'] }}</h3>
                            <p class="text-muted mb-0">Réservations confirmées</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-users text-info" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-info mb-1">{{ $stats['total_users'] }}</h3>
                            <p class="text-muted mb-0">Utilisateurs actifs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <i class="fas fa-store text-warning" style="font-size: 2.5rem;"></i>
                            </div>
                            <h3 class="h2 fw-bold text-warning mb-1">{{ $stats['total_sellers'] }}</h3>
                            <p class="text-muted mb-0">Propriétaires partenaires</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Locaux populaires -->
    @if($popularLocals->count() > 0)
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Locaux les plus populaires</h2>
                <p class="lead text-muted">Découvrez les espaces préférés de nos utilisateurs</p>
            </div>
            <div class="row g-4">
                @foreach($popularLocals as $local)
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="position-relative">
                            @if($local->image)
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center"
                                     style="height: 200px;">
                                    @if($local->type === 'sport')
                                        <i class="fas fa-futbol text-white fa-3x"></i>
                                    @elseif($local->type === 'conference')
                                        <i class="fas fa-presentation-screen text-white fa-3x"></i>
                                    @else
                                        <i class="fas fa-glass-cheers text-white fa-3x"></i>
                                    @endif
                                </div>
                            @endif
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-danger">
                                    <i class="fas fa-fire me-1"></i>Populaire
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fw-bold">{{ $local->name }}</h5>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $local->location }}
                            </p>
                            <p class="text-muted small mb-3">
                                <i class="fas fa-users me-1"></i>{{ $local->capacity }} personnes
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if($local->pricing_type === 'flexible')
                                        <span class="fw-bold text-primary">{{ $local->hourly_price }} MAD/h</span>
                                    @else
                                        <span class="fw-bold text-primary">{{ $local->price }} MAD</span>
                                    @endif
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-calendar-check me-1"></i>{{ $local->reservations_count }} réservations
                                </small>
                            </div>
                            <a href="{{ route('locals.show', $local) }}" class="btn btn-primary w-100 mt-3">
                                <i class="fas fa-eye me-2"></i>Voir détails
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Section Locaux récents -->
    @if($featuredLocals->count() > 0)
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Derniers locaux ajoutés</h2>
                <p class="lead text-muted">Découvrez les nouveaux espaces disponibles</p>
            </div>
            <div class="row g-4">
                @foreach($featuredLocals->take(3) as $local)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="position-relative">
                            @if($local->image)
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="card-img-top"
                                     style="height: 200px; object-fit: cover;">
                            @else
                                <div class="card-img-top bg-gradient-secondary d-flex align-items-center justify-content-center"
                                     style="height: 200px;">
                                    @if($local->type === 'sport')
                                        <i class="fas fa-futbol text-white fa-3x"></i>
                                    @elseif($local->type === 'conference')
                                        <i class="fas fa-presentation-screen text-white fa-3x"></i>
                                    @else
                                        <i class="fas fa-glass-cheers text-white fa-3x"></i>
                                    @endif
                                </div>
                            @endif
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success">
                                    <i class="fas fa-star me-1"></i>Nouveau
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fw-bold">{{ $local->name }}</h5>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>{{ $local->location }}
                            </p>
                            <p class="text-muted small mb-3">
                                <i class="fas fa-users me-1"></i>{{ $local->capacity }} personnes
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if($local->pricing_type === 'flexible')
                                        <span class="fw-bold text-primary">{{ $local->hourly_price }} MAD/h</span>
                                    @else
                                        <span class="fw-bold text-primary">{{ $local->price }} MAD</span>
                                    @endif
                                </div>
                                <small class="text-success">
                                    <i class="fas fa-clock me-1"></i>Ajouté {{ $local->created_at->diffForHumans() }}
                                </small>
                            </div>
                            <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-primary w-100 mt-3">
                                <i class="fas fa-eye me-2"></i>Découvrir
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($featuredLocals->count() > 3)
            <div class="text-center mt-4">
                <a href="{{ route('locals.index') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>Voir tous les locaux
                </a>
            </div>
            @endif
        </div>
    </section>
    @endif

@endsection

@push('styles')
<style>
/* Hover effects */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Floating animation */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Fade in animations */
.fade-in-left {
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card improvements */
.card {
    border-radius: 15px;
    overflow: hidden;
}

.card-img-top {
    border-radius: 15px 15px 0 0;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Button improvements */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Statistics section */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .hero-content {
        text-align: center;
    }

    .hero-logo-container {
        margin-top: 2rem;
    }
}

/* Loading animation for images */
.card-img-top {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.card-img-top img {
    animation: none;
    background: none;
}
</style>
@endpush