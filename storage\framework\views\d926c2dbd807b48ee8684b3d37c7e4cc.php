<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                    </h1>
                    <p class="text-muted">Bienvenue, <?php echo e(Auth::user()->name); ?> !</p>
                </div>
                <div>
                    <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['total_reservations']); ?></h4>
                            <p class="mb-0">Total réservations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['confirmed_reservations']); ?></h4>
                            <p class="mb-0">Confirmées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['pending_reservations']); ?></h4>
                            <p class="mb-0">En attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($stats['cancelled_reservations']); ?></h4>
                            <p class="mb-0">Annulées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Reservations -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>Prochaines réservations
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($upcomingReservations->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $upcomingReservations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e($reservation->local->name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($reservation->local->location); ?></small>
                                        </td>
                                        <td><?php echo e($reservation->date->format('d/m/Y')); ?></td>
                                        <td><?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?></td>
                                        <td>
                                            <?php if($reservation->status === 'confirmée'): ?>
                                                <span class="badge bg-success">Confirmée</span>
                                            <?php elseif($reservation->status === 'en attente'): ?>
                                                <span class="badge bg-warning">En attente</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Annulée</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('reservations.show', $reservation)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-primary">
                                Voir toutes mes réservations
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5>Aucune réservation à venir</h5>
                            <p class="text-muted">Commencez par réserver un local !</p>
                            <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Faire une réservation
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Parcourir les locaux
                        </a>
                        <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Mes réservations
                        </a>
                        <a href="<?php echo e(route('profile.show')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>Mon profil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Available Locals Preview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Locaux populaires
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($availableLocals->count() > 0): ?>
                        <?php $__currentLoopData = $availableLocals->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $local): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <?php if($local->type === 'sport'): ?>
                                    <i class="fas fa-futbol text-success fa-2x"></i>
                                <?php elseif($local->type === 'conference'): ?>
                                    <i class="fas fa-presentation-screen text-primary fa-2x"></i>
                                <?php else: ?>
                                    <i class="fas fa-glass-cheers text-warning fa-2x"></i>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0"><?php echo e($local->name); ?></h6>
                                <small class="text-muted"><?php echo e($local->location); ?></small><br>
                                <small class="text-success fw-bold"><?php echo e(abs($local->price)); ?> MAD/h</small>
                            </div>
                            <div>
                                <a href="<?php echo e(route('locals.show', $local)); ?>" class="btn btn-sm btn-outline-primary">
                                    Voir
                                </a>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('locals.index')); ?>" class="btn btn-sm btn-outline-primary">
                                Voir tous les locaux
                            </a>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">Aucun local disponible</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/dashboard.blade.php ENDPATH**/ ?>