@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Modifier le type: {{ $localType->name }}
                    </h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.local-types.update', $localType) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Nom (EN) *</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $localType->name) }}" 
                                           required
                                           placeholder="Ex: Office">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name_fr" class="form-label">Nom (FR)</label>
                                    <input type="text" 
                                           class="form-control @error('name_fr') is-invalid @enderror" 
                                           id="name_fr" 
                                           name="name_fr" 
                                           value="{{ old('name_fr', $localType->name_fr) }}"
                                           placeholder="Ex: Bureau">
                                    @error('name_fr')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="name_ar" class="form-label">Nom (AR)</label>
                            <input type="text" 
                                   class="form-control @error('name_ar') is-invalid @enderror" 
                                   id="name_ar" 
                                   name="name_ar" 
                                   value="{{ old('name_ar', $localType->name_ar) }}"
                                   placeholder="Ex: مكتب"
                                   dir="rtl">
                            @error('name_ar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Description du type de local...">{{ old('description', $localType->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="icon" class="form-label">Icône FontAwesome *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i id="icon-preview" class="{{ $localType->icon }}"></i>
                                        </span>
                                        <input type="text" 
                                               class="form-control @error('icon') is-invalid @enderror" 
                                               id="icon" 
                                               name="icon" 
                                               value="{{ old('icon', $localType->icon) }}" 
                                               required
                                               placeholder="fas fa-building">
                                    </div>
                                    <small class="form-text text-muted">
                                        Utilisez les classes FontAwesome (ex: fas fa-building, fas fa-users)
                                    </small>
                                    @error('icon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="color" class="form-label">Couleur *</label>
                                    <div class="input-group">
                                        <input type="color" 
                                               class="form-control form-control-color @error('color') is-invalid @enderror" 
                                               id="color" 
                                               name="color" 
                                               value="{{ old('color', $localType->color) }}" 
                                               required>
                                        <input type="text" 
                                               class="form-control" 
                                               id="color-text" 
                                               value="{{ old('color', $localType->color) }}" 
                                               readonly>
                                    </div>
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="sort_order" class="form-label">Ordre d'affichage</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', $localType->sort_order) }}" 
                                           min="0"
                                           placeholder="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" 
                                               class="form-check-input" 
                                               id="active" 
                                               name="active" 
                                               value="1" 
                                               {{ old('active', $localType->active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="active">
                                            Type actif
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($localType->locals()->count() > 0)
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Ce type est utilisé par <strong>{{ $localType->locals()->count() }}</strong> local(aux).
                            </div>
                        @endif

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.local-types.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('color-text');

    // Update icon preview
    iconInput.addEventListener('input', function() {
        iconPreview.className = this.value || 'fas fa-building';
    });

    // Update color text
    colorInput.addEventListener('input', function() {
        colorText.value = this.value;
        iconPreview.style.color = this.value;
    });

    // Initial color setup
    iconPreview.style.color = colorInput.value;
});
</script>
@endpush
