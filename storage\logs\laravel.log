[2025-05-27 22:53:26] local.ERROR: There are no commands defined in the "linke" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"linke\" namespace. at C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('linke')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('linke:storage')
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-05-27 22:53:31] local.ERROR: There are no commands defined in the "link" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"link\" namespace. at C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('link')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('link:storage')
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\N\\test\\locaspace\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-05-27 22:55:04] local.INFO: Stripe createPaymentIntent called {"invoice_id":"13","amount":"20.00"} 
[2025-05-27 22:55:04] local.INFO: Invoice found {"invoice_id":13,"user_id":1} 
