<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Local;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Check local availability
Route::post('/locals/{local}/check-availability', function (Request $request, Local $local) {
    try {
        $request->validate([
            'date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'all_day' => 'nullable|boolean',
        ]);

        // Handle all-day reservations
        if ($request->boolean('all_day')) {
            $startTime = '00:00';
            $endTime = '23:59';
        } else {
            $startTime = $request->start_time ?? '09:00';
            $endTime = $request->end_time ?? '10:00';

            // Validate that end time is after start time
            if ($endTime <= $startTime) {
                return response()->json([
                    'available' => false,
                    'message' => 'L\'heure de fin doit être après l\'heure de début.'
                ]);
            }
        }

        // Check if user already has a pending reservation for the same local and date
        if (auth()->check()) {
            $existingPendingReservation = \App\Models\Reservation::where('user_id', auth()->id())
                ->where('local_id', $local->id)
                ->where('date', $request->date)
                ->where('status', 'en attente')
                ->first();

            if ($existingPendingReservation) {
                return response()->json([
                    'available' => false,
                    'message' => "Vous avez déjà une réservation en attente pour ce local à cette date ({$existingPendingReservation->start_time->format('H:i')} - {$existingPendingReservation->end_time->format('H:i')}). Veuillez finaliser ou annuler votre réservation existante avant d'en créer une nouvelle."
                ]);
            }
        }

        $available = $local->isAvailable(
            $request->date,
            $startTime,
            $endTime
        );

        $message = '';
        if (!$available) {
            // Get conflicting reservations for better error message
            $conflicts = $local->reservations()
                ->where('date', $request->date)
                ->where('status', '!=', 'annulée')
                ->where(function ($q) use ($startTime, $endTime) {
                    $q->where(function ($q2) use ($startTime, $endTime) {
                        $q2->where('start_time', '<', $endTime)
                           ->where('end_time', '>', $startTime);
                    });
                })
                ->orderBy('start_time')
                ->get();

            if ($conflicts->count() > 0) {
                $conflictTimes = $conflicts->map(function($conflict) {
                    return $conflict->start_time->format('H:i') . ' - ' . $conflict->end_time->format('H:i');
                })->join(', ');

                if ($request->boolean('all_day')) {
                    $message = "Le local n'est pas disponible toute la journée. Créneaux occupés : {$conflictTimes}";
                } else {
                    $message = "Conflit avec les réservations existantes : {$conflictTimes}";
                }
            } else {
                $message = "Ce créneau n'est pas disponible.";
            }
        } else {
            if ($request->boolean('all_day')) {
                $message = "Local disponible toute la journée !";
            } else {
                $message = "Créneau disponible de {$startTime} à {$endTime} !";
            }
        }

        return response()->json([
            'available' => $available,
            'message' => $message,
            'requested_time' => [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'all_day' => $request->boolean('all_day')
            ]
        ]);

    } catch (\Exception $e) {
        \Log::error('Availability check error: ' . $e->getMessage());

        return response()->json([
            'available' => false,
            'message' => 'Erreur lors de la vérification de disponibilité. Veuillez réessayer.',
            'error' => $e->getMessage()
        ], 500);
    }
});

// Calendar reservations data
Route::get('/calendar/reservations', function (Request $request) {
    $year = $request->get('year', now()->year);
    $month = $request->get('month', now()->month);

    // Get all reservations for the specified month
    $reservations = \App\Models\Reservation::with(['local'])
        ->whereYear('date', $year)
        ->whereMonth('date', $month)
        ->where('status', '!=', 'annulée')
        ->orderBy('date')
        ->orderBy('start_time')
        ->get();

    // Group reservations by date
    $groupedReservations = [];

    foreach ($reservations as $reservation) {
        $dateKey = $reservation->date->format('Y-m-d');

        if (!isset($groupedReservations[$dateKey])) {
            $groupedReservations[$dateKey] = [];
        }

        $groupedReservations[$dateKey][] = [
            'id' => $reservation->id,
            'local_name' => $reservation->local->name,
            'location' => $reservation->local->location,
            'start_time' => $reservation->start_time->format('H:i'),
            'end_time' => $reservation->end_time->format('H:i'),
            'status' => $reservation->status,
            'user_name' => $reservation->user->name ?? 'Utilisateur',
            'pricing_type' => $reservation->selected_pricing_type,
        ];
    }

    // Get statistics for the month
    $stats = [
        'total_reservations' => $reservations->count(),
        'confirmed_reservations' => $reservations->where('status', 'confirmée')->count(),
        'pending_reservations' => $reservations->where('status', 'en attente')->count(),
        'busiest_day' => $reservations->groupBy(function($item) {
            return $item->date->format('Y-m-d');
        })->sortByDesc(function($dayReservations) {
            return $dayReservations->count();
        })->keys()->first(),
    ];

    return response()->json([
        'reservations' => $groupedReservations,
        'stats' => $stats,
        'month' => $month,
        'year' => $year,
    ]);
});

// Local-specific calendar data
Route::get('/locals/{local}/calendar', function (Request $request, Local $local) {
    $year = $request->get('year', now()->year);
    $month = $request->get('month', now()->month);

    // Get reservations for this specific local for the month
    $reservations = $local->reservations()
        ->whereYear('date', $year)
        ->whereMonth('date', $month)
        ->where('status', '!=', 'annulée')
        ->orderBy('date')
        ->orderBy('start_time')
        ->get();

    // Group reservations by date
    $groupedReservations = [];

    foreach ($reservations as $reservation) {
        $dateKey = $reservation->date->format('Y-m-d');

        if (!isset($groupedReservations[$dateKey])) {
            $groupedReservations[$dateKey] = [];
        }

        $groupedReservations[$dateKey][] = [
            'id' => $reservation->id,
            'start_time' => $reservation->start_time->format('H:i'),
            'end_time' => $reservation->end_time->format('H:i'),
            'status' => $reservation->status,
            'user_name' => $reservation->user->name ?? 'Utilisateur',
            'duration' => $reservation->start_time->diffInHours($reservation->end_time),
        ];
    }

    // Calculate availability statistics for the month
    $totalDaysInMonth = now()->setYear($year)->setMonth($month)->daysInMonth;
    $daysWithReservations = count($groupedReservations);
    $availableDays = $totalDaysInMonth - $daysWithReservations;

    // Get busiest times (most common booking hours)
    $timeSlots = [];
    foreach ($reservations as $reservation) {
        $hour = $reservation->start_time->format('H');
        if (!isset($timeSlots[$hour])) {
            $timeSlots[$hour] = 0;
        }
        $timeSlots[$hour]++;
    }
    arsort($timeSlots);

    return response()->json([
        'reservations' => $groupedReservations,
        'local' => [
            'id' => $local->id,
            'name' => $local->name,
            'pricing_type' => $local->pricing_type,
            'hourly_price' => $local->hourly_price ?? $local->price,
            'daily_price' => $local->daily_price,
            'weekly_price' => $local->weekly_price,
            'monthly_price' => $local->monthly_price,
        ],
        'stats' => [
            'total_reservations' => $reservations->count(),
            'confirmed_reservations' => $reservations->where('status', 'confirmée')->count(),
            'pending_reservations' => $reservations->where('status', 'en attente')->count(),
            'available_days' => $availableDays,
            'busiest_times' => array_slice($timeSlots, 0, 3, true),
        ],
        'month' => $month,
        'year' => $year,
    ]);
});
