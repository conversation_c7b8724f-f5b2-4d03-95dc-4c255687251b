<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Local;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Check local availability
Route::post('/locals/{local}/check-availability', function (Request $request, Local $local) {
    $request->validate([
        'date' => 'required|date',
        'start_time' => 'required|date_format:H:i',
        'end_time' => 'required|date_format:H:i|after:start_time',
    ]);

    // Check if user already has a pending reservation for the same local and date
    if (auth()->check()) {
        $existingPendingReservation = \App\Models\Reservation::where('user_id', auth()->id())
            ->where('local_id', $local->id)
            ->where('date', $request->date)
            ->where('status', 'en attente')
            ->first();

        if ($existingPendingReservation) {
            return response()->json([
                'available' => false,
                'message' => "Vous avez déjà une réservation en attente pour ce local à cette date ({$existingPendingReservation->start_time} - {$existingPendingReservation->end_time}). Veuillez finaliser ou annuler votre réservation existante avant d'en créer une nouvelle."
            ]);
        }
    }

    $available = $local->isAvailable(
        $request->date,
        $request->start_time,
        $request->end_time
    );

    $message = '';
    if (!$available) {
        // Get conflicting reservations for better error message
        $conflicts = $local->reservations()
            ->where('date', $request->date)
            ->where('status', '!=', 'annulée')
            ->where(function ($q) use ($request) {
                $q->whereBetween('start_time', [$request->start_time, $request->end_time])
                  ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                  ->orWhere(function ($q2) use ($request) {
                      $q2->where('start_time', '<=', $request->start_time)
                         ->where('end_time', '>=', $request->end_time);
                  });
            })
            ->get();

        if ($conflicts->count() > 0) {
            $conflict = $conflicts->first();
            $message = "Il y a déjà une réservation de {$conflict->start_time} à {$conflict->end_time}.";
        }
    }

    return response()->json([
        'available' => $available,
        'message' => $message
    ]);
});

// Calendar reservations data
Route::get('/calendar/reservations', function (Request $request) {
    $year = $request->get('year', now()->year);
    $month = $request->get('month', now()->month);

    // Get all reservations for the specified month
    $reservations = \App\Models\Reservation::with(['local'])
        ->whereYear('date', $year)
        ->whereMonth('date', $month)
        ->where('status', '!=', 'annulée')
        ->orderBy('date')
        ->orderBy('start_time')
        ->get();

    // Group reservations by date
    $groupedReservations = [];

    foreach ($reservations as $reservation) {
        $dateKey = $reservation->date->format('Y-m-d');

        if (!isset($groupedReservations[$dateKey])) {
            $groupedReservations[$dateKey] = [];
        }

        $groupedReservations[$dateKey][] = [
            'id' => $reservation->id,
            'local_name' => $reservation->local->name,
            'location' => $reservation->local->location,
            'start_time' => $reservation->start_time->format('H:i'),
            'end_time' => $reservation->end_time->format('H:i'),
            'status' => $reservation->status,
            'user_name' => $reservation->user->name ?? 'Utilisateur',
            'pricing_type' => $reservation->selected_pricing_type,
        ];
    }

    // Get statistics for the month
    $stats = [
        'total_reservations' => $reservations->count(),
        'confirmed_reservations' => $reservations->where('status', 'confirmée')->count(),
        'pending_reservations' => $reservations->where('status', 'en attente')->count(),
        'busiest_day' => $reservations->groupBy(function($item) {
            return $item->date->format('Y-m-d');
        })->sortByDesc(function($dayReservations) {
            return $dayReservations->count();
        })->keys()->first(),
    ];

    return response()->json([
        'reservations' => $groupedReservations,
        'stats' => $stats,
        'month' => $month,
        'year' => $year,
    ]);
});
