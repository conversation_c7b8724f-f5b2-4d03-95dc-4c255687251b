<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Local;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Check local availability
Route::post('/locals/{local}/check-availability', function (Request $request, Local $local) {
    $request->validate([
        'date' => 'required|date',
        'start_time' => 'required|date_format:H:i',
        'end_time' => 'required|date_format:H:i|after:start_time',
    ]);

    $available = $local->isAvailable(
        $request->date,
        $request->start_time,
        $request->end_time
    );

    $message = '';
    if (!$available) {
        // Get conflicting reservations for better error message
        $conflicts = $local->reservations()
            ->where('date', $request->date)
            ->where('status', '!=', 'annulée')
            ->where(function ($q) use ($request) {
                $q->whereBetween('start_time', [$request->start_time, $request->end_time])
                  ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                  ->orWhere(function ($q2) use ($request) {
                      $q2->where('start_time', '<=', $request->start_time)
                         ->where('end_time', '>=', $request->end_time);
                  });
            })
            ->get();

        if ($conflicts->count() > 0) {
            $conflict = $conflicts->first();
            $message = "Il y a déjà une réservation de {$conflict->start_time} à {$conflict->end_time}.";
        }
    }

    return response()->json([
        'available' => $available,
        'message' => $message
    ]);
});
