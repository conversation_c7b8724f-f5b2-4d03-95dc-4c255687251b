<?php

namespace App\Policies;

use App\Models\Local;
use App\Models\User;

class LocalPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Local $local): bool
    {
        // Admins can view all locals
        if ($user->isAdmin()) {
            return true;
        }

        // Sellers can view their own locals
        if ($user->isSeller() && $local->seller_id === $user->id) {
            return true;
        }

        // Clients can view active locals
        return $local->status === true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isSeller() || $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Local $local): bool
    {
        // Admins can update all locals
        if ($user->isAdmin()) {
            return true;
        }

        // Sellers can update their own locals
        return $user->isSeller() && $local->seller_id === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Local $local): bool
    {
        // Admins can delete all locals
        if ($user->isAdmin()) {
            return true;
        }

        // Sellers can delete their own locals (with restrictions)
        return $user->isSeller() && $local->seller_id === $user->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Local $local): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Local $local): bool
    {
        return $user->isAdmin();
    }
}
