@extends('layouts.app')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/locals-page.css') }}">
@endpush

@section('content')
<div class="container">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="hero-section text-center py-5 position-relative overflow-hidden"
                 style="background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%); border-radius: 20px;">
                <!-- Background Animation -->
                <div class="position-absolute w-100 h-100" style="opacity: 0.1;">
                    <div class="position-absolute" style="top: 20%; left: 10%; animation: float 6s ease-in-out infinite;">
                        <i class="fas fa-building fa-3x text-white"></i>
                    </div>
                    <div class="position-absolute" style="top: 30%; right: 15%; animation: float 8s ease-in-out infinite reverse;">
                        <i class="fas fa-futbol fa-2x text-white"></i>
                    </div>
                    <div class="position-absolute" style="bottom: 20%; left: 20%; animation: float 7s ease-in-out infinite;">
                        <i class="fas fa-glass-cheers fa-2x text-white"></i>
                    </div>
                </div>

                <div class="position-relative z-index-1">
                    <h1 class="display-4 fw-bold text-white mb-3 fade-in-up">
                        <i class="fas fa-building me-3 text-warning"></i>
                        Découvrez nos <span class="text-warning">locaux</span>
                    </h1>
                    <p class="lead text-white-50 mb-4 fade-in-up" style="animation-delay: 0.2s;">
                        Trouvez l'espace parfait pour vos événements, réunions et activités
                    </p>
                    <div class="fade-in-up" style="animation-delay: 0.4s;">
                        <span class="badge bg-warning text-dark fs-6 me-3">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ $locals->count() }} locaux disponibles
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-star me-1"></i>Réservation instantanée
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Filtres de recherche
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="GET" action="{{ route('locals.index') }}" id="filterForm">
                        <div class="row g-4">
                            <div class="col-lg-3 col-md-6">
                                <label for="type" class="form-label fw-semibold">
                                    <i class="fas fa-tags me-1 text-primary"></i>Type de local
                                </label>
                                <select name="type" id="type" class="form-select form-select-lg">
                                    <option value="">🏢 Tous les types</option>
                                    <option value="sport" {{ request('type') === 'sport' ? 'selected' : '' }}>
                                        ⚽ Terrains de sport
                                    </option>
                                    <option value="conference" {{ request('type') === 'conference' ? 'selected' : '' }}>
                                        💼 Salles de conférences
                                    </option>
                                    <option value="fête" {{ request('type') === 'fête' ? 'selected' : '' }}>
                                        🎉 Salles de fêtes
                                    </option>
                                </select>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label for="location" class="form-label fw-semibold">
                                    <i class="fas fa-map-marker-alt me-1 text-danger"></i>Localisation
                                </label>
                                <input type="text" name="location" id="location" class="form-control form-control-lg"
                                       placeholder="🌍 Ville, quartier..." value="{{ request('location') }}">
                            </div>
                            <div class="col-lg-2 col-md-6">
                                <label for="capacity" class="form-label fw-semibold">
                                    <i class="fas fa-users me-1 text-info"></i>Capacité min.
                                </label>
                                <input type="number" name="capacity" id="capacity" class="form-control form-control-lg"
                                       placeholder="👥 Ex: 10" value="{{ request('capacity') }}">
                            </div>
                            <div class="col-lg-2 col-md-6">
                                <label for="equipment" class="form-label fw-semibold">
                                    <i class="fas fa-cogs me-1 text-warning"></i>Équipement
                                </label>
                                <select name="equipment" id="equipment" class="form-select form-select-lg">
                                    <option value="">🔧 Tous</option>
                                    <option value="wifi" {{ request('equipment') === 'wifi' ? 'selected' : '' }}>
                                        📶 WiFi
                                    </option>
                                    <option value="projecteur" {{ request('equipment') === 'projecteur' ? 'selected' : '' }}>
                                        📽️ Projecteur
                                    </option>
                                    <option value="climatisation" {{ request('equipment') === 'climatisation' ? 'selected' : '' }}>
                                        ❄️ Climatisation
                                    </option>
                                </select>
                            </div>
                            <div class="col-lg-2 col-md-12">
                                <div class="form-label fw-semibold">&nbsp;</div>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>Rechercher
                                    </button>
                                    <a href="{{ route('locals.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-refresh me-1"></i>Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>
                    Résultats de recherche
                    <span class="badge bg-primary ms-2">{{ $locals->count() }} trouvé(s)</span>
                </h3>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('grid')" id="gridViewBtn">
                        <i class="fas fa-th-large me-1"></i>Grille
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('list')" id="listViewBtn">
                        <i class="fas fa-list me-1"></i>Liste
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="row" id="localsContainer">
        @if($locals->count() > 0)
            @foreach($locals as $index => $local)
            <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12 mb-4 local-card fade-in-up" style="animation-delay: {{ $index * 0.1 }}s;">
                <div class="card h-100 border-0 shadow-lg local-item" data-type="{{ $local->type }}">
                    <!-- Card Image/Icon -->
                    <div class="card-img-top position-relative overflow-hidden" style="height: 200px;">
                        @if($local->image)
                            <!-- Image du local -->
                            <img src="{{ asset('storage/' . $local->image) }}"
                                 alt="{{ $local->name }}"
                                 class="w-100 h-100"
                                 style="object-fit: cover;">
                            <!-- Overlay gradient pour lisibilité -->
                            <div class="position-absolute top-0 start-0 w-100 h-100"
                                 style="background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);"></div>
                        @else
                            <!-- Gradient de couleur selon le type -->
                            <div class="w-100 h-100" style="background: linear-gradient(135deg,
                                @if($local->type === 'sport') #28a745, #20c997
                                @elseif($local->type === 'conference') #007bff, #6610f2
                                @else #ffc107, #fd7e14 @endif);">
                            </div>
                            <!-- Icône centrale -->
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <x-local-icon :type="$local->type" size="xl" class="text-white opacity-75" />
                            </div>
                        @endif

                        <!-- Badge type -->
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-white text-dark fs-6 px-3 py-2 shadow-sm">
                                {{ ucfirst($local->type) }}
                            </span>
                        </div>

                        <!-- Prix -->
                        <div class="position-absolute bottom-0 start-0 m-3">
                            <div class="price-tag bg-white rounded-pill px-3 py-2 shadow">
                                <span class="h5 text-success mb-0 fw-bold">{{ abs($local->price) }} MAD</span>
                                <small class="text-muted">/heure</small>
                            </div>
                        </div>
                    </div>

                    <!-- Card Content -->
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold mb-3">{{ $local->name }}</h5>

                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                <span class="text-muted">{{ $local->location }}</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users text-info me-2"></i>
                                <span class="text-muted">Capacité : <strong>{{ $local->capacity }}</strong> personnes</span>
                            </div>
                        </div>

                        @if($local->equipment && count($local->equipment) > 0)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-cogs me-1"></i>Équipements :
                            </h6>
                            <div class="d-flex flex-wrap gap-1">
                                @foreach($local->equipment as $equipment)
                                    <span class="badge bg-light text-dark border">
                                        @if($equipment === 'wifi')
                                            📶 WiFi
                                        @elseif($equipment === 'projecteur')
                                            📽️ Projecteur
                                        @elseif($equipment === 'climatisation')
                                            ❄️ Climatisation
                                        @else
                                            🔧 {{ $equipment }}
                                        @endif
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Rating Stars (placeholder) -->
                        <div class="mb-3">
                            <div class="d-flex align-items-center">
                                <div class="text-warning me-2">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= 4 ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                                <small class="text-muted">(4.0/5 - 12 avis)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Card Actions -->
                    <div class="card-footer bg-transparent border-0 p-4 pt-0">
                        <div class="d-grid gap-2">
                            @auth
                                <a href="{{ route('reservations.create', $local) }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-plus me-2"></i>Réserver maintenant
                                </a>
                                <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>Voir les détails
                                </a>
                            @else
                                <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter pour réserver
                                </a>
                                <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>Voir les détails
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-search fa-4x text-muted mb-3 opacity-50"></i>
                            <h3 class="text-muted">Aucun local trouvé</h3>
                            <p class="lead text-muted">
                                Nous n'avons trouvé aucun local correspondant à vos critères de recherche.
                            </p>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-2"></i>Suggestions :</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li>• Essayez de modifier vos critères de recherche</li>
                                        <li>• Vérifiez l'orthographe de la localisation</li>
                                        <li>• Réduisez la capacité minimale</li>
                                        <li>• Supprimez certains filtres</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center gap-3 mt-4">
                            <a href="{{ route('locals.index') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-refresh me-2"></i>Réinitialiser les filtres
                            </a>
                            <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-home me-2"></i>Retour à l'accueil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if($locals->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                <div class="pagination-wrapper custom-pagination">
                    {{ $locals->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Stats -->
    @if($locals->count() > 0)
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-primary">{{ $locals->where('type', 'sport')->count() }}</h3>
                                <p class="text-muted mb-0">⚽ Terrains de sport</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-info">{{ $locals->where('type', 'conference')->count() }}</h3>
                                <p class="text-muted mb-0">💼 Salles de conférence</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-warning">{{ $locals->where('type', 'fête')->count() }}</h3>
                                <p class="text-muted mb-0">🎉 Salles de fête</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-success">{{ number_format($locals->avg('price'), 0) }}MAD</h3>
                                <p class="text-muted mb-0">💰 Prix moyen/heure</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
// View Toggle Functionality
let currentView = 'grid';

function toggleView(view) {
    const container = document.getElementById('localsContainer');
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');

    if (view === 'list') {
        container.classList.add('list-view');
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
        currentView = 'list';
    } else {
        container.classList.remove('list-view');
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
        currentView = 'grid';
    }

    // Save preference
    localStorage.setItem('localsView', view);
}

// Filter Enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Load saved view preference
    const savedView = localStorage.getItem('localsView') || 'grid';
    toggleView(savedView);

    // Auto-submit form on filter change
    const filterForm = document.getElementById('filterForm');
    const filterInputs = filterForm.querySelectorAll('select, input');

    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add loading state
            const submitBtn = filterForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Recherche...';
            submitBtn.disabled = true;

            // Submit form
            setTimeout(() => {
                filterForm.submit();
            }, 500);
        });
    });

    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all local cards
    document.querySelectorAll('.local-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease-out';
        observer.observe(card);
    });
});

// Search functionality
function quickSearch(type) {
    const typeSelect = document.getElementById('type');
    typeSelect.value = type;
    document.getElementById('filterForm').submit();
}

// Price range filter (if needed)
function filterByPrice(min, max) {
    // Implementation for price filtering
    console.log(`Filter by price: ${min} - ${max}`);
}
</script>

<style>
/* List View Styles */
.list-view .local-card {
    margin-bottom: 1rem;
}

.list-view .local-card .card {
    flex-direction: row;
    max-height: 200px;
}

.list-view .card-img-top {
    width: 200px;
    height: 200px !important;
    flex-shrink: 0;
}

.list-view .card-body {
    flex: 1;
    padding: 1.5rem;
}

.list-view .card-footer {
    width: 200px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Stat Items */
.stat-item {
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Button Active State */
.btn.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

/* Price Tag Animation */
.price-tag {
    transition: all 0.3s ease;
}

.card:hover .price-tag {
    transform: scale(1.05);
}

/* Loading Animation */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Custom Pagination Styles */
.custom-pagination .pagination {
    margin-bottom: 0;
}

.custom-pagination .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.2s ease-in-out;
}

.custom-pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    transform: translateY(-1px);
}

.custom-pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.25);
}

.custom-pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

.custom-pagination .page-item:first-child .page-link,
.custom-pagination .page-item:last-child .page-link {
    border-radius: 0.375rem;
}

/* Compact pagination for mobile */
@media (max-width: 576px) {
    .custom-pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        margin: 0 0.0625rem;
    }

    .custom-pagination .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .list-view .card {
        flex-direction: column;
        max-height: none;
    }

    .list-view .card-img-top {
        width: 100%;
        height: 200px !important;
    }

    .list-view .card-footer {
        width: 100%;
    }
}
</style>
@endpush
@endsection
