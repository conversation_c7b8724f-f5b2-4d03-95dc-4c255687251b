<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'LocaSpace')); ?> - <?php echo $__env->yieldContent('title', 'Authentification'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Theme CSS (optional) -->
    <?php if(file_exists(public_path('css/theme.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/theme.css')); ?>">
    <?php endif; ?>

    <!-- Custom Navbar CSS (optional) -->
    <?php if(file_exists(public_path('css/navbar.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/navbar.css')); ?>">
    <?php endif; ?>

    <!-- Navbar Fix CSS (optional) -->
    <?php if(file_exists(public_path('css/navbar-fix.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/navbar-fix.css')); ?>">
    <?php endif; ?>

    <!-- Modern Styles CSS (optional) -->
    <?php if(file_exists(public_path('css/modern-styles.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/modern-styles.css')); ?>">
    <?php endif; ?>

    <!-- Layout Improvements CSS (optional) -->
    <?php if(file_exists(public_path('css/layout-improvements.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/layout-improvements.css')); ?>">
    <?php endif; ?>

    <!-- QR Scanner CSS (optional) -->
    <?php if(file_exists(public_path('css/qr-scanner.css'))): ?>
        <link rel="stylesheet" href="<?php echo e(asset('css/qr-scanner.css')); ?>">
    <?php endif; ?>

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Page Specific Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>

    <style>
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .dropdown-header {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .dropdown-item-title {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .dropdown-item-text {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
        }

        .sidebar {
            min-height: calc(100vh - 76px);
            background-color: #f8f9fa;
        }

        .main-content {
            min-height: calc(100vh - 76px);
        }

        .badge {
            font-size: 0.6rem;
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Navbar improvements */
        .navbar-toggler {
            border: none;
            padding: 0.25rem 0.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .navbar-collapse {
            flex-basis: 100%;
            flex-grow: 1;
            align-items: center;
        }

        /* Responsive improvements */
        @media (max-width: 991.98px) {
            .navbar-nav {
                text-align: center;
            }

            .navbar-nav .nav-link {
                padding: 0.75rem 1rem;
            }

            .navbar-nav .dropdown-menu {
                border: 1px solid rgba(0, 0, 0, 0.15);
                margin-top: 0.5rem;
                position: static !important;
                transform: none !important;
                box-shadow: none;
                border-radius: 0;
                background-color: rgba(255, 255, 255, 0.95);
            }

            .dropdown-item:hover {
                transform: none;
                background-color: rgba(0, 123, 255, 0.1);
            }

            .navbar-nav .badge {
                display: inline-block;
                margin-left: 0.5rem;
            }
        }

        @media (min-width: 992px) {
            .navbar-expand-lg .navbar-nav .dropdown-menu {
                position: absolute;
            }
        }

        /* Auth page specific styles */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .auth-container {
            width: 100%;
            max-width: 450px;
            padding: 2rem;
            margin: 0 auto;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
        }

        .auth-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        /* Auth page specific styles for forms */
        .auth-header {
            text-align: center;
            padding: 3rem 2rem 1rem;
            position: relative;
        }

        .logo-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .logo-container i {
            font-size: 2rem;
            color: white;
            position: relative;
            z-index: 1;
        }

        .auth-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 0.5rem;
            letter-spacing: -0.02em;
        }

        .auth-subtitle {
            color: #6b7280;
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .auth-body {
            padding: 0 2rem 3rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }

        .form-label i {
            margin-right: 0.5rem;
            color: #667eea;
            width: 16px;
        }

        .auth-card .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fafafa;
            font-weight: 500;
        }

        .auth-card .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .auth-card .form-control::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .auth-card .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .auth-card .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .auth-card .btn-primary:active {
            transform: translateY(0);
        }

        .auth-card .btn-outline-primary {
            border: 2px solid #e5e7eb;
            color: #374151;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            background: white;
            transition: all 0.3s ease;
        }

        .auth-card .btn-outline-primary:hover {
            border-color: #667eea;
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-1px);
        }

        .divider {
            position: relative;
            text-align: center;
            margin: 2rem 0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 1rem;
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .auth-footer {
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
            padding: 1rem 2rem;
        }

        .auth-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            color: #764ba2;
        }

        .auth-card .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .auth-card .form-check-input {
            width: 18px;
            height: 18px;
            margin-right: 0.75rem;
            border: 2px solid #d1d5db;
            border-radius: 4px;
        }

        .auth-card .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .auth-card .form-check-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0;
            cursor: pointer;
        }

        .auth-card .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .auth-card .alert-info {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: #667eea;
            border-left: 4px solid #667eea;
        }

        @media (max-width: 768px) {
            .auth-container {
                padding: 1rem;
            }

            .auth-header {
                padding: 2rem 1.5rem 1rem;
            }

            .auth-body {
                padding: 0 1.5rem 2rem;
            }

            .auth-title {
                font-size: 1.75rem;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <?php if (isset($component)) { $__componentOriginala591787d01fe92c5706972626cdf7231 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala591787d01fe92c5706972626cdf7231 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala591787d01fe92c5706972626cdf7231)): ?>
<?php $attributes = $__attributesOriginala591787d01fe92c5706972626cdf7231; ?>
<?php unset($__attributesOriginala591787d01fe92c5706972626cdf7231); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala591787d01fe92c5706972626cdf7231)): ?>
<?php $component = $__componentOriginala591787d01fe92c5706972626cdf7231; ?>
<?php unset($__componentOriginala591787d01fe92c5706972626cdf7231); ?>
<?php endif; ?>

        <!-- Navbar Spacer -->
        <div class="navbar-spacer"></div>

        <!-- Main Content -->
        <main class="py-4">
            <div class="container-fluid d-flex align-items-center justify-content-center" style="min-height: calc(100vh - 76px);">
                <div class="auth-container">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- QR Scanner JS -->
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>

    <!-- Custom JS (optional) -->
    <?php if(file_exists(public_path('js/navbar.js'))): ?>
        <script src="<?php echo e(asset('js/navbar.js')); ?>"></script>
    <?php endif; ?>
    <?php if(file_exists(public_path('js/notifications.js'))): ?>
        <script src="<?php echo e(asset('js/notifications.js')); ?>"></script>
    <?php endif; ?>
    <?php if(file_exists(public_path('js/qr-scanner.js'))): ?>
        <script src="<?php echo e(asset('js/qr-scanner.js')); ?>"></script>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/layouts/auth.blade.php ENDPATH**/ ?>