<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Local;
use Illuminate\Support\Facades\Hash;

class SellerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test seller users
        $sellers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'seller',
                'business_name' => '<PERSON>\'s Spaces',
                'business_description' => 'Professional office and meeting spaces in Casablanca',
                'phone' => '+212 6 12 34 56 78',
                'address' => 'Casablanca, Morocco',
                'verified_seller' => true,
                'seller_approved_at' => now(),
            ],
            [
                'name' => 'Fatima Business',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'seller',
                'business_name' => 'Fatima Event Spaces',
                'business_description' => 'Premium event venues and conference halls',
                'phone' => '+212 6 87 65 43 21',
                'address' => 'Rabat, Morocco',
                'verified_seller' => true,
                'seller_approved_at' => now(),
            ],
            [
                'name' => 'Youssef Properties',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'seller',
                'business_name' => 'Youssef Co-working',
                'business_description' => 'Modern co-working spaces and studios',
                'phone' => '+212 6 11 22 33 44',
                'address' => 'Marrakech, Morocco',
                'verified_seller' => true,
                'seller_approved_at' => now(),
            ],
        ];

        foreach ($sellers as $sellerData) {
            // Check if user already exists
            $seller = User::where('email', $sellerData['email'])->first();

            if (!$seller) {
                $seller = User::create($sellerData);
                echo "Created seller: " . $seller->name . "\n";
            } else {
                echo "Seller already exists: " . $seller->name . "\n";
                // Update existing seller with new data
                $seller->update($sellerData);
            }

            // Create sample locals for each seller (only if they don't have any)
            if ($seller->locals()->count() === 0) {
                $this->createSampleLocals($seller);
                echo "Created sample locals for: " . $seller->name . "\n";
            } else {
                echo "Seller already has locals: " . $seller->name . "\n";
            }
        }
    }

    /**
     * Create sample locals for a seller.
     */
    private function createSampleLocals(User $seller): void
    {
        $localsData = [
            [
                'name' => 'Modern Office Space',
                'description' => 'A fully equipped modern office space perfect for small teams and startups. Features high-speed internet, modern furniture, and a professional atmosphere.',
                'type' => 'Office',
                'location' => $seller->address,
                'capacity' => 10,
                'equipment' => ['WiFi', 'Air Conditioning', 'Parking', 'Printer'],
                'pricing_type' => 'flexible',
                'hourly_price' => 50.00,
                'daily_price' => 300.00,
                'weekly_price' => 1800.00,
                'monthly_price' => 6000.00,
                'minimum_rental_hours' => 2,
                'available_24_7' => false,
                'approval_required' => 'manual',
                'status' => true,
            ],
            [
                'name' => 'Conference Room Premium',
                'description' => 'Professional conference room with state-of-the-art presentation equipment. Ideal for business meetings, presentations, and corporate events.',
                'type' => 'Meeting Room',
                'location' => $seller->address,
                'capacity' => 20,
                'equipment' => ['WiFi', 'Projector', 'Whiteboard', 'Air Conditioning', 'Sound System'],
                'pricing_type' => 'flexible',
                'hourly_price' => 80.00,
                'daily_price' => 500.00,
                'weekly_price' => 3000.00,
                'minimum_rental_hours' => 1,
                'available_24_7' => false,
                'approval_required' => 'automatic',
                'status' => true,
            ],
            [
                'name' => 'Creative Studio Space',
                'description' => 'Bright and inspiring creative studio perfect for workshops, photo shoots, and creative sessions. Natural lighting and flexible layout.',
                'type' => 'Studio',
                'location' => $seller->address,
                'capacity' => 15,
                'equipment' => ['WiFi', 'Air Conditioning', 'Parking'],
                'pricing_type' => 'flexible',
                'hourly_price' => 60.00,
                'daily_price' => 400.00,
                'weekly_price' => 2400.00,
                'monthly_price' => 8000.00,
                'minimum_rental_hours' => 3,
                'maximum_rental_hours' => 48,
                'available_24_7' => false,
                'available_hours' => [
                    'monday' => ['start' => '08:00', 'end' => '20:00'],
                    'tuesday' => ['start' => '08:00', 'end' => '20:00'],
                    'wednesday' => ['start' => '08:00', 'end' => '20:00'],
                    'thursday' => ['start' => '08:00', 'end' => '20:00'],
                    'friday' => ['start' => '08:00', 'end' => '20:00'],
                    'saturday' => ['start' => '10:00', 'end' => '18:00'],
                ],
                'approval_required' => 'manual',
                'rental_terms' => 'No smoking, no food allowed, clean up after use.',
                'status' => true,
            ],
        ];

        foreach ($localsData as $localData) {
            $localData['seller_id'] = $seller->id;
            $localData['price'] = $localData['hourly_price'] ?? 100.00; // Fallback price
            Local::create($localData);
        }
    }
}
