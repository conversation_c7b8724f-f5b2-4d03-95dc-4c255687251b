<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Local;
use Illuminate\Support\Facades\Validator;

class LocalController extends Controller
{
    /**
     * Display a listing of locals.
     */
    public function index(Request $request)
    {
        $query = Local::active();

        // Filter by type
        if ($request->filled('type')) {
            $query->ofType($request->type);
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        // Filter by capacity
        if ($request->filled('capacity')) {
            $query->where('capacity', '>=', $request->capacity);
        }

        // Filter by equipment
        if ($request->filled('equipment')) {
            $equipment = $request->equipment;
            $query->whereJsonContains('equipment', $equipment);
        }

        $locals = $query->paginate(12);

        return view('locals.index', compact('locals'));
    }

    /**
     * Show the form for creating a new local.
     */
    public function create()
    {
        return view('locals.create');
    }

    /**
     * Store a newly created local in storage.
     */
    public function store(Request $request)
    {
        $validationRules = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|max:100',
            'location' => 'required|string|max:255',
            'capacity' => 'nullable|integer|min:1',
            'equipment' => 'nullable|array',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_available' => 'nullable|boolean',
            'pricing_type' => 'required|in:fixed,flexible',
            'minimum_rental_hours' => 'nullable|integer|min:1',
            'maximum_rental_hours' => 'nullable|integer|min:1',
            'available_24_7' => 'nullable|boolean',
            'approval_required' => 'required|in:manual,automatic',
        ];

        // Add pricing validation based on pricing type
        if ($request->pricing_type === 'fixed') {
            $validationRules['price'] = 'required|numeric|min:0';
        } else {
            $validationRules['hourly_price'] = 'required|numeric|min:0';
            $validationRules['daily_price'] = 'nullable|numeric|min:0';
            $validationRules['weekly_price'] = 'nullable|numeric|min:0';
            $validationRules['monthly_price'] = 'nullable|numeric|min:0';
            $validationRules['yearly_price'] = 'nullable|numeric|min:0';
        }

        $validator = Validator::make($request->all(), $validationRules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Handle image upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('locals', 'public');
        }

        $localData = [
            'name' => $request->name,
            'description' => $request->description,
            'type' => $request->type,
            'location' => $request->location,
            'capacity' => $request->capacity ?? 1,
            'equipment' => $request->equipment ?? [],
            'image' => $imagePath,
            'status' => $request->boolean('is_available', true),
            'pricing_type' => $request->pricing_type,
            'minimum_rental_hours' => $request->minimum_rental_hours ?? 1,
            'maximum_rental_hours' => $request->maximum_rental_hours,
            'available_24_7' => $request->boolean('available_24_7', false),
            'approval_required' => $request->approval_required,
        ];

        // Add pricing fields based on type
        if ($request->pricing_type === 'fixed') {
            $localData['price'] = $request->price;
        } else {
            $localData['hourly_price'] = $request->hourly_price;
            $localData['daily_price'] = $request->daily_price;
            $localData['weekly_price'] = $request->weekly_price;
            $localData['monthly_price'] = $request->monthly_price;
            $localData['yearly_price'] = $request->yearly_price;
            // Set the main price field to hourly price for backward compatibility
            $localData['price'] = $request->hourly_price;
        }

        Local::create($localData);

        return redirect()->route('locals.index')->with('success', 'Local créé avec succès.');
    }

    /**
     * Display the specified local.
     */
    public function show(Local $local)
    {
        return view('locals.show', compact('local'));
    }

    /**
     * Show the form for editing the specified local.
     */
    public function edit(Local $local)
    {
        return view('locals.edit', compact('local'));
    }

    /**
     * Update the specified local in storage.
     */
    public function update(Request $request, Local $local)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|in:sport,conference,fête',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'equipment' => 'array',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $local->update([
            'name' => $request->name,
            'type' => $request->type,
            'location' => $request->location,
            'capacity' => $request->capacity,
            'price' => $request->price,
            'equipment' => $request->equipment ?? [],
            'status' => $request->boolean('status'),
        ]);

        return redirect()->route('locals.index')->with('success', 'Local mis à jour avec succès.');
    }

    /**
     * Remove the specified local from storage.
     */
    public function destroy(Local $local)
    {
        $local->delete();
        return redirect()->route('locals.index')->with('success', 'Local supprimé avec succès.');
    }

    /**
     * Get availability for a specific local.
     */
    public function availability(Request $request, Local $local)
    {
        $date = $request->get('date', now()->format('Y-m-d'));

        $reservations = $local->reservations()
            ->onDate($date)
            ->where('status', '!=', 'annulée')
            ->get(['start_time', 'end_time', 'status']);

        return response()->json([
            'date' => $date,
            'reservations' => $reservations,
        ]);
    }
}
