<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar bg-gradient-primary">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                    <span><i class="fas fa-store me-2"></i>Tableau de bord vendeur</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.locals')); ?>">
                            <i class="fas fa-building me-2"></i> Mes locaux
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active bg-white text-primary rounded mx-2" href="<?php echo e(route('seller.locals.create')); ?>">
                            <i class="fas fa-plus me-2"></i> Ajouter un nouveau local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.reservations')); ?>">
                            <i class="fas fa-calendar-check me-2"></i> Réservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4 bg-light min-vh-100">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                <div>
                    <h1 class="h2 text-primary mb-1">
                        <i class="fas fa-plus-circle me-2"></i>Ajouter un nouveau local
                    </h1>
                    <p class="text-muted">Créez un nouveau local pour votre activité de location</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo e(route('seller.locals')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i> Retour aux locaux
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0 rounded-3">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations du local</h5>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('seller.locals.store')); ?>" method="POST" enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>

                                <!-- Informations de base -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Nom du local *</label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="name" name="name" value="<?php echo e(old('name')); ?>" required
                                                   placeholder="Ex: Salle de réunion moderne">
                                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="type">Type de local *</label>
                                            <select class="form-control <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="type" name="type" required>
                                                <option value="">Sélectionner le type</option>
                                                <?php $__currentLoopData = \App\Models\LocalType::active()->ordered()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($localType->name); ?>"
                                                            <?php echo e(old('type') == $localType->name ? 'selected' : ''); ?>

                                                            data-icon="<?php echo e($localType->icon); ?>"
                                                            data-color="<?php echo e($localType->color); ?>">
                                                        <?php echo e($localType->display_name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description détaillée *</label>
                                    <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="description" name="description" rows="4" required
                                              placeholder="Décrivez votre local en détail : équipements, ambiance, points forts..."><?php echo e(old('description')); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="location">Adresse complète *</label>
                                            <input type="text" class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="location" name="location" value="<?php echo e(old('location')); ?>" required
                                                   placeholder="Ex: 123 Rue Mohammed V, Casablanca">
                                            <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="capacity">Capacité maximale (personnes) *</label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="capacity" name="capacity" value="<?php echo e(old('capacity')); ?>" min="1" required
                                                   placeholder="Ex: 20">
                                            <?php $__errorArgs = ['capacity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Tarification -->
                                <div class="card mt-4">
                                    <div class="card-header bg-gradient-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-euro-sign me-2"></i>Configuration des tarifs</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label class="form-label">Type de tarification *</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="fixed_pricing" value="fixed" <?php echo e(old('pricing_type', 'fixed') == 'fixed' ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="fixed_pricing">
                                                    <strong>Prix fixe</strong> (par réservation)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="flexible_pricing" value="flexible" <?php echo e(old('pricing_type') == 'flexible' ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="flexible_pricing">
                                                    <strong>Tarification flexible</strong> (horaire, journalier, hebdomadaire, mensuel, annuel)
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Prix fixe -->
                                        <div id="fixed_price_section" class="pricing-section">
                                            <div class="form-group">
                                                <label for="price">Prix fixe (MAD) *</label>
                                                <input type="number" class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                       id="price" name="price" value="<?php echo e(old('price')); ?>" step="0.01" min="0"
                                                       placeholder="Ex: 500">
                                                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                        </div>

                                        <!-- Tarification flexible -->
                                        <div id="flexible_price_section" class="pricing-section" style="display: none;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="hourly_price">Prix horaire (MAD) *</label>
                                                        <input type="number" class="form-control <?php $__errorArgs = ['hourly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                               id="hourly_price" name="hourly_price" value="<?php echo e(old('hourly_price')); ?>" step="0.01" min="0"
                                                               placeholder="Ex: 50">
                                                        <?php $__errorArgs = ['hourly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="daily_price">Prix journalier (MAD)</label>
                                                        <input type="number" class="form-control <?php $__errorArgs = ['daily_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                               id="daily_price" name="daily_price" value="<?php echo e(old('daily_price')); ?>" step="0.01" min="0"
                                                               placeholder="Ex: 300">
                                                        <?php $__errorArgs = ['daily_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="weekly_price">Prix hebdomadaire (MAD)</label>
                                                        <input type="number" class="form-control <?php $__errorArgs = ['weekly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                               id="weekly_price" name="weekly_price" value="<?php echo e(old('weekly_price')); ?>" step="0.01" min="0"
                                                               placeholder="Ex: 1800">
                                                        <?php $__errorArgs = ['weekly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="monthly_price">Prix mensuel (MAD)</label>
                                                        <input type="number" class="form-control <?php $__errorArgs = ['monthly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                               id="monthly_price" name="monthly_price" value="<?php echo e(old('monthly_price')); ?>" step="0.01" min="0"
                                                               placeholder="Ex: 6000">
                                                        <?php $__errorArgs = ['monthly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="yearly_price">Prix annuel (MAD)</label>
                                                        <input type="number" class="form-control <?php $__errorArgs = ['yearly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                               id="yearly_price" name="yearly_price" value="<?php echo e(old('yearly_price')); ?>" step="0.01" min="0"
                                                               placeholder="Ex: 60000">
                                                        <?php $__errorArgs = ['yearly_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Disponibilité -->
                                <div class="card mt-4">
                                    <div class="card-header bg-gradient-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Paramètres de disponibilité</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="minimum_rental_hours">Durée minimale de location (heures) *</label>
                                                    <input type="number" class="form-control <?php $__errorArgs = ['minimum_rental_hours'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="minimum_rental_hours" name="minimum_rental_hours" value="<?php echo e(old('minimum_rental_hours', 1)); ?>" min="1" required
                                                           placeholder="Ex: 2">
                                                    <?php $__errorArgs = ['minimum_rental_hours'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="maximum_rental_hours">Durée maximale de location (heures)</label>
                                                    <input type="number" class="form-control <?php $__errorArgs = ['maximum_rental_hours'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="maximum_rental_hours" name="maximum_rental_hours" value="<?php echo e(old('maximum_rental_hours')); ?>" min="1"
                                                           placeholder="Ex: 24">
                                                    <small class="form-text text-muted">Laisser vide pour aucune limite</small>
                                                    <?php $__errorArgs = ['maximum_rental_hours'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="available_24_7" name="available_24_7" value="1" <?php echo e(old('available_24_7') ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="available_24_7">
                                                    Disponible 24h/24 et 7j/7
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="approval_required">Approbation des réservations *</label>
                                            <select class="form-control <?php $__errorArgs = ['approval_required'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="approval_required" name="approval_required" required>
                                                <option value="manual" <?php echo e(old('approval_required', 'manual') == 'manual' ? 'selected' : ''); ?>>Approbation manuelle requise</option>
                                                <option value="automatic" <?php echo e(old('approval_required') == 'automatic' ? 'selected' : ''); ?>>Approbation automatique</option>
                                            </select>
                                            <?php $__errorArgs = ['approval_required'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Équipements et Image -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="equipment">Équipements disponibles</label>
                                            <div class="equipment-checkboxes">
                                                <?php
                                                    $equipmentOptions = [
                                                        'WiFi' => 'WiFi',
                                                        'Projector' => 'Projecteur',
                                                        'Whiteboard' => 'Tableau blanc',
                                                        'Air Conditioning' => 'Climatisation',
                                                        'Parking' => 'Parking',
                                                        'Kitchen' => 'Cuisine',
                                                        'Printer' => 'Imprimante',
                                                        'Sound System' => 'Système audio'
                                                    ];
                                                ?>
                                                <?php $__currentLoopData = $equipmentOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="<?php echo e($value); ?>" id="equipment_<?php echo e($loop->index); ?>"
                                                               <?php echo e(in_array($value, old('equipment', [])) ? 'checked' : ''); ?>>
                                                        <label class="form-check-label" for="equipment_<?php echo e($loop->index); ?>">
                                                            <?php echo e($label); ?>

                                                        </label>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="image">Image du local</label>
                                            <input type="file" class="form-control-file <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">Téléchargez une image de votre local (max 2MB)</small>
                                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="rental_terms">Conditions de location</label>
                                    <textarea class="form-control <?php $__errorArgs = ['rental_terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="rental_terms" name="rental_terms" rows="3"
                                              placeholder="Ex: Interdiction de fumer, nettoyage après utilisation..."><?php echo e(old('rental_terms')); ?></textarea>
                                    <?php $__errorArgs = ['rental_terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="form-group text-right">
                                    <button type="button" class="btn btn-secondary mr-2" onclick="history.back()">Annuler</button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>Créer le local
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Section d'aide -->
                <div class="col-lg-4">
                    <div class="card shadow-lg border-0 rounded-3">
                        <div class="card-header bg-gradient-success text-white">
                            <h6 class="mb-0">💡 Conseils pour réussir</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Photos de qualité</strong> augmentent les réservations de 40%
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Descriptions détaillées</strong> aident les clients à comprendre votre espace
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Prix compétitifs</strong> attirent plus de clients
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Disponibilité flexible</strong> augmente les opportunités de réservation
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Réponses rapides</strong> aux demandes créent la confiance
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="card shadow-lg border-0 rounded-3 mt-3">
                        <div class="card-header bg-gradient-info text-white">
                            <h6 class="mb-0">📋 Guide des tarifs</h6>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted">
                                <strong>Avantages de la tarification flexible :</strong><br>
                                • <strong>Horaire :</strong> Parfait pour les réunions courtes<br>
                                • <strong>Journalier :</strong> Idéal pour les ateliers<br>
                                • <strong>Hebdomadaire :</strong> Parfait pour les projets<br>
                                • <strong>Mensuel :</strong> Optimal pour les clients long terme<br>
                                • <strong>Annuel :</strong> Valeur maximale pour les installations permanentes
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fixedRadio = document.getElementById('fixed_pricing');
    const flexibleRadio = document.getElementById('flexible_pricing');
    const fixedSection = document.getElementById('fixed_price_section');
    const flexibleSection = document.getElementById('flexible_price_section');

    function togglePricingSections() {
        if (fixedRadio.checked) {
            fixedSection.style.display = 'block';
            flexibleSection.style.display = 'none';
        } else {
            fixedSection.style.display = 'none';
            flexibleSection.style.display = 'block';
        }
    }

    fixedRadio.addEventListener('change', togglePricingSections);
    flexibleRadio.addEventListener('change', togglePricingSections);

    // Initialize on page load
    togglePricingSections();
});
</script>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #007bff !important;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.hover-white:hover {
    color: white !important;
}

/* Main Content */
main {
    margin-left: 16.66667%;
}

/* Form Enhancements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* Equipment Checkboxes */
.equipment-checkboxes {
    max-height: 250px;
    overflow-y: auto;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background-color: #f8f9fa;
}

.equipment-checkboxes .form-check {
    margin-bottom: 0.75rem;
}

.equipment-checkboxes .form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Pricing Sections */
.pricing-section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 2px solid #e9ecef;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    transform: translateY(-2px);
}

/* Background Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        padding: 1rem 0;
    }

    main {
        margin-left: 0;
    }

    .card-body {
        padding: 1rem;
    }

    .pricing-section {
        padding: 1rem;
    }
}

/* Animation for form sections */
.pricing-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Select Styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/seller/locals/create.blade.php ENDPATH**/ ?>