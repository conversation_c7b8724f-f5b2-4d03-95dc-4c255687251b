<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <div class="hero-icon mb-3">
                    <i class="fas fa-qrcode fa-4x text-primary"></i>
                </div>
                <h1 class="display-5 fw-bold text-primary mb-3">Connexion QR Code</h1>
                <p class="lead text-muted">
                    Scannez un QR code pour vous connecter rapidement et en toute sécurité
                </p>
            </div>

            <!-- Options de connexion -->
            <div class="row g-4">
                <!-- Scanner un QR code -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-lg">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-camera fa-3x text-primary"></i>
                            </div>
                            <h4 class="card-title mb-3">Scanner un QR Code</h4>
                            <p class="card-text text-muted mb-4">
                                Utilisez votre caméra pour scanner un QR code d'authentification
                            </p>
                            <button class="btn btn-primary btn-lg qr-scan-btn" data-qr-scan>
                                <i class="fas fa-camera me-2"></i>
                                Démarrer le scan
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Afficher votre QR code -->
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-lg">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-qrcode fa-3x text-warning"></i>
                            </div>
                            <h4 class="card-title mb-3">Votre QR Code</h4>
                            <p class="card-text text-muted mb-4">
                                Générez votre QR code personnel pour vous connecter sur d'autres appareils
                            </p>
                            <?php if(auth()->guard()->check()): ?>
                                <button class="btn btn-warning btn-lg qr-generate-btn" onclick="generateUserQR()">
                                    <i class="fas fa-qrcode me-2"></i>
                                    Générer mon QR
                                </button>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-warning btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Se connecter d'abord
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="card mt-5 border-0 bg-light">
                <div class="card-body p-4">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        Comment ça marche ?
                    </h5>
                    <div class="row g-3 mt-2">
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">1</div>
                                <div>
                                    <h6>Scanner</h6>
                                    <small class="text-muted">Cliquez sur "Démarrer le scan" et autorisez l'accès à la caméra</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">2</div>
                                <div>
                                    <h6>Viser</h6>
                                    <small class="text-muted">Pointez votre caméra vers le QR code LocaSpace</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-start">
                                <div class="step-number me-3">3</div>
                                <div>
                                    <h6>Connexion</h6>
                                    <small class="text-muted">Vous serez automatiquement connecté</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Avertissement HTTPS -->
            <?php if(!request()->secure() && !app()->environment('local')): ?>
            <div class="alert alert-warning mt-4" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Attention :</strong> Pour utiliser la caméra, une connexion HTTPS est requise.
                <a href="<?php echo e(str_replace('http://', 'https://', request()->url())); ?>" class="alert-link">
                    Passer en HTTPS
                </a>
            </div>
            <?php endif; ?>

            <!-- Connexion classique -->
            <div class="text-center mt-4">
                <p class="text-muted">
                    Vous préférez la connexion classique ?
                    <a href="<?php echo e(route('login')); ?>" class="text-primary text-decoration-none">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        Se connecter avec email/mot de passe
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Fonction pour générer le QR code utilisateur
async function generateUserQR() {
    try {
        const response = await fetch('/api/qr/generate-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showQRModal(data.qr_image, data.user.name);
        } else {
            alert('Erreur lors de la génération du QR code');
        }
    } catch (error) {
        console.error('Erreur:', error);
        alert('Erreur de connexion');
    }
}

// Afficher le QR code dans une modal
function showQRModal(qrImage, userName) {
    const modalHTML = `
        <div id="user-qr-modal" class="qr-scanner-modal">
            <div class="qr-scanner-content">
                <div class="qr-scanner-header">
                    <h3><i class="fas fa-qrcode me-2"></i>Votre QR Code</h3>
                    <button class="qr-close-btn" onclick="document.getElementById('user-qr-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="qr-scanner-body text-center">
                    <div class="mb-3">
                        <img src="${qrImage}" alt="QR Code" class="img-fluid" style="max-width: 256px; border-radius: 15px;">
                    </div>
                    <div class="qr-info">
                        <h5 class="mb-2">${userName}</h5>
                        <p class="text-muted mb-0">Scannez ce code avec un autre appareil</p>
                    </div>
                </div>
                <div class="qr-scanner-footer">
                    <button class="btn btn-secondary" onclick="document.getElementById('user-qr-modal').remove()">Fermer</button>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    document.getElementById('user-qr-modal').style.display = 'block';
}
</script>

<style>
.hero-icon { animation: float 3s ease-in-out infinite; }
@keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
.feature-icon { animation: pulse 2s ease-in-out infinite; }
@keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
.step-number {
    width: 30px; height: 30px; background: linear-gradient(135deg, #4f46e5, #3730a3);
    color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center;
    font-weight: bold; font-size: 0.875rem; flex-shrink: 0;
}
.card:hover { transform: translateY(-5px); transition: all 0.3s ease; }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/auth/qr-login.blade.php ENDPATH**/ ?>