<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\Conversation;
use App\Models\Local;
use App\Models\Reservation;
use App\Models\User;

class ChatbotController extends Controller
{
    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'session_id' => 'nullable|string'
        ]);

        $userMessage = $request->input('message');
        $sessionId = $request->input('session_id', Str::uuid());
        $user = Auth::user();

        // Get conversation history for context
        $conversationHistory = Conversation::getSessionHistory($sessionId, 5);

        // Build context about the website
        $websiteContext = $this->buildWebsiteContext($user);

        // Build messages array for OpenAI
        $messages = $this->buildMessagesArray($conversationHistory, $websiteContext);
        $messages[] = ['role' => 'user', 'content' => $userMessage];

        try {
            $response = Http::withToken(env('OPENAI_API_KEY'))
                ->timeout(30)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o-mini',
                    'messages' => $messages,
                    'max_tokens' => 1000,
                    'temperature' => 0.7,
                    'presence_penalty' => 0.1,
                    'frequency_penalty' => 0.1,
                ]);

            if ($response->failed()) {
                return response()->json([
                    'error' => 'Error communicating with OpenAI',
                    'session_id' => $sessionId
                ], 500);
            }

            $chatbotReply = $response->json('choices.0.message.content');

            // Save conversation to database
            $conversation = Conversation::create([
                'user_id' => $user?->id,
                'session_id' => $sessionId,
                'user_message' => $userMessage,
                'bot_response' => $chatbotReply,
                'context_data' => [
                    'user_authenticated' => $user !== null,
                    'timestamp' => now()->toISOString(),
                ],
                'conversation_type' => $this->detectConversationType($userMessage),
            ]);

            return response()->json([
                'reply' => $chatbotReply,
                'session_id' => $sessionId,
                'conversation_id' => $conversation->id,
                'timestamp' => $conversation->created_at->toISOString()
            ]);

        } catch (\Exception $e) {
            \Log::error('Chatbot error: ' . $e->getMessage());

            return response()->json([
                'error' => 'Sorry, I\'m having trouble right now. Please try again later.',
                'session_id' => $sessionId
            ], 500);
        }
    }

    /**
     * Build website context for the chatbot
     */
    private function buildWebsiteContext($user)
    {
        $context = [
            'website_name' => 'LocaSpace',
            'website_purpose' => 'Platform for booking and managing local spaces in Morocco',
            'available_features' => [
                'Browse available locals/spaces',
                'Make reservations',
                'Manage bookings',
                'View invoices',
                'QR code authentication',
                'Stripe payment integration',
                'User profile management',
                'Admin dashboard (for admins)',
            ],
            'main_pages' => [
                'Home' => '/',
                'Locals' => '/locals',
                'Dashboard' => '/dashboard',
                'Reservations' => '/reservations',
                'Profile' => '/profile',
            ]
        ];

        if ($user) {
            $context['user_info'] = [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'is_authenticated' => true,
                'total_reservations' => $user->reservations()->count(),
                'active_reservations' => $user->reservations()->where('status', 'confirmée')->count(),
            ];

            // Add recent reservations
            $recentReservations = $user->reservations()
                ->with('local')
                ->latest()
                ->limit(3)
                ->get()
                ->map(function ($reservation) {
                    return [
                        'id' => $reservation->id,
                        'local_name' => $reservation->local->name,
                        'date' => $reservation->date,
                        'status' => $reservation->status,
                    ];
                });

            $context['user_info']['recent_reservations'] = $recentReservations;
        } else {
            $context['user_info'] = [
                'is_authenticated' => false,
                'available_actions' => ['login', 'register', 'browse_locals']
            ];
        }

        // Add available locals info
        $availableLocals = Local::where('status', true)
            ->limit(5)
            ->get(['id', 'name', 'type', 'location', 'price'])
            ->map(function ($local) {
                return [
                    'id' => $local->id,
                    'name' => $local->name,
                    'type' => $local->type,
                    'location' => $local->location,
                    'price' => $local->price,
                ];
            });

        $context['available_locals'] = $availableLocals;

        return $context;
    }

    /**
     * Build messages array for OpenAI API
     */
    private function buildMessagesArray($conversationHistory, $websiteContext)
    {
        $systemPrompt = $this->buildSystemPrompt($websiteContext);

        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];

        // Add conversation history
        foreach ($conversationHistory as $conversation) {
            $messages[] = ['role' => 'user', 'content' => $conversation->user_message];
            $messages[] = ['role' => 'assistant', 'content' => $conversation->bot_response];
        }

        return $messages;
    }

    /**
     * Build comprehensive system prompt
     */
    private function buildSystemPrompt($context)
    {
        $userInfo = $context['user_info'];
        $availableLocals = $context['available_locals'];

        $prompt = "You are LocaBot, the intelligent assistant for LocaSpace - Morocco's premier platform for booking and managing local spaces. You are helpful, friendly, and knowledgeable about the platform.

**Your Role:**
- Website guide and assistant
- Help users navigate the platform
- Provide information about available spaces
- Assist with booking processes
- Answer questions about features and services
- Offer personalized recommendations

**Platform Information:**
- Website: LocaSpace
- Purpose: {$context['website_purpose']}
- Main Features: " . implode(', ', $context['available_features']) . "

**Available Pages:**
";

        foreach ($context['main_pages'] as $page => $url) {
            $prompt .= "- {$page}: {$url}\n";
        }

        if ($userInfo['is_authenticated']) {
            $prompt .= "\n**Current User:**
- Name: {$userInfo['name']}
- Role: {$userInfo['role']}
- Total Reservations: {$userInfo['total_reservations']}
- Active Reservations: {$userInfo['active_reservations']}
";

            if (!empty($userInfo['recent_reservations'])) {
                $prompt .= "\n**Recent Reservations:**\n";
                foreach ($userInfo['recent_reservations'] as $reservation) {
                    $prompt .= "- {$reservation['local_name']} on {$reservation['date']} (Status: {$reservation['status']})\n";
                }
            }
        } else {
            $prompt .= "\n**User Status:** Not authenticated
**Available Actions:** " . implode(', ', $userInfo['available_actions']);
        }

        if (!empty($availableLocals)) {
            $prompt .= "\n\n**Currently Available Spaces:**\n";
            foreach ($availableLocals as $local) {
                $prompt .= "- {$local['name']} ({$local['type']}) in {$local['location']} - {$local['price']} MAD\n";
            }
        }

        $prompt .= "\n\n**Guidelines:**
1. Always be helpful and professional
2. Provide specific information when possible
3. Guide users to relevant pages when appropriate
4. If asked about bookings, explain the process step by step
5. For technical issues, suggest contacting support
6. Use emojis sparingly but appropriately
7. Keep responses concise but informative
8. Always respond in the same language the user writes in (French, Arabic, or English)
9. If you don't know something specific, admit it and suggest alternatives

**Common Tasks You Can Help With:**
- Finding available spaces
- Explaining booking process
- Navigating the website
- Understanding features
- Account management
- Payment information
- Troubleshooting basic issues

Remember: You're here to make the user's experience with LocaSpace as smooth and helpful as possible!";

        return $prompt;
    }

    /**
     * Detect conversation type based on user message
     */
    private function detectConversationType($message)
    {
        $message = strtolower($message);

        if (str_contains($message, 'book') || str_contains($message, 'reserve') || str_contains($message, 'réserver')) {
            return 'booking';
        }

        if (str_contains($message, 'help') || str_contains($message, 'support') || str_contains($message, 'aide') || str_contains($message, 'problem')) {
            return 'support';
        }

        if (str_contains($message, 'how') || str_contains($message, 'what') || str_contains($message, 'where') || str_contains($message, 'comment') || str_contains($message, 'quoi') || str_contains($message, 'où')) {
            return 'guide';
        }

        return 'general';
    }

    /**
     * Get user conversations
     */
    public function getConversations(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $conversations = Conversation::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json($conversations);
    }

    /**
     * Delete a specific conversation
     */
    public function deleteConversation(Conversation $conversation)
    {
        $user = Auth::user();

        if (!$user || $conversation->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $conversation->delete();

        return response()->json(['message' => 'Conversation deleted successfully']);
    }

    /**
     * Clear all user conversations
     */
    public function clearAllConversations(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $deletedCount = Conversation::where('user_id', $user->id)->delete();

        return response()->json([
            'message' => 'All conversations cleared successfully',
            'deleted_count' => $deletedCount
        ]);
    }

    /**
     * Provide feedback on conversation
     */
    public function provideFeedback(Request $request, Conversation $conversation)
    {
        $request->validate([
            'is_helpful' => 'required|boolean',
            'feedback' => 'nullable|string|max:500'
        ]);

        $user = Auth::user();

        if (!$user || $conversation->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $conversation->update([
            'is_helpful' => $request->is_helpful,
            'feedback' => $request->feedback
        ]);

        return response()->json(['message' => 'Feedback saved successfully']);
    }
}
