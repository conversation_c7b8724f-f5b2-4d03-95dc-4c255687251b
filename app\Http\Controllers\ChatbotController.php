<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ChatbotController extends Controller
{
      public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $userMessage = $request->input('message');
        $messages = session('chat_history', [
            ['role' => 'system', 'content' => "
            
            You are a helpful assistant.
            
            "],
        ]);
        $messages[] = ['role' => 'user', 'content' => $userMessage];

        $response = Http::withToken(env('OPENAI_API_KEY'))
            ->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-4o', 
                'messages' => $messages,
                'max_tokens' => 1000000 , //!(1m token only for testing)
                'temperature' => 0.7,
            ]);

        if ($response->failed()) {
            return response()->json(['message' => 'Error communicating with OpenAI'], 500);
        }

        $chatbotReply = $response->json('choices.0.message.content');

        return response()->json(['reply' => $chatbotReply]);
    }
}
