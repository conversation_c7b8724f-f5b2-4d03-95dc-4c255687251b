<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use App\Models\Local;
use App\Models\User;
use App\Models\Reservation;

class SellerController extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user() || (!Auth::user()->isSeller() && !Auth::user()->isAdmin())) {
                abort(403, 'Access denied. Seller role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display seller dashboard.
     */
    public function dashboard()
    {
        $seller = Auth::user();

        $stats = [
            'total_locals' => $seller->locals()->count(),
            'active_locals' => $seller->activeLocals()->count(),
            'total_reservations' => $seller->locals()->withCount('reservations')->get()->sum('reservations_count'),
            'pending_reservations' => $this->getPendingReservations($seller)->count(),
            'total_earnings' => $seller->totalEarnings(),
            'this_month_earnings' => $this->getMonthlyEarnings($seller),
        ];

        $recentReservations = $this->getRecentReservations($seller);
        $topLocals = $this->getTopPerformingLocals($seller);

        return view('seller.dashboard', compact('stats', 'recentReservations', 'topLocals'));
    }

    /**
     * Display seller's locals.
     */
    public function locals()
    {
        $locals = Auth::user()->locals()->with('reservations')->paginate(10);
        return view('seller.locals.index', compact('locals'));
    }

    /**
     * Show form to create new local.
     */
    public function createLocal()
    {
        return view('seller.locals.create');
    }

    /**
     * Store new local.
     */
    public function storeLocal(Request $request)
    {
        // Debug: Log the request data
        \Log::info('Seller local creation attempt', [
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|string|max:100',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'equipment' => 'nullable|array',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',

            // Pricing fields
            'pricing_type' => 'required|in:fixed,flexible',
            'price' => 'required_if:pricing_type,fixed|nullable|numeric|min:0',
            'hourly_price' => 'required_if:pricing_type,flexible|nullable|numeric|min:0',
            'daily_price' => 'nullable|numeric|min:0',
            'weekly_price' => 'nullable|numeric|min:0',
            'monthly_price' => 'nullable|numeric|min:0',
            'yearly_price' => 'nullable|numeric|min:0',

            // Availability fields
            'minimum_rental_hours' => 'required|integer|min:1',
            'maximum_rental_hours' => 'nullable|integer|min:1',
            'available_24_7' => 'boolean',
            'available_hours' => 'nullable|array',
            'rental_terms' => 'nullable|string',
            'approval_required' => 'required|in:automatic,manual',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('locals', 'public');
        }

        // Add seller ID and status
        $validated['seller_id'] = Auth::id();
        $validated['status'] = true; // Active by default

        // Ensure price field is always set for backward compatibility
        if ($validated['pricing_type'] === 'flexible') {
            $validated['price'] = $validated['hourly_price']; // Use hourly as base price
        }

        $local = Local::create($validated);

        return redirect()->route('seller.locals.show', $local)
            ->with('success', 'Local created successfully!');
    }

    /**
     * Show specific local.
     */
    public function showLocal(Local $local)
    {
        $this->authorize('view', $local);

        $local->load(['reservations.user', 'seller']);
        $stats = $this->getLocalStats($local);

        return view('seller.locals.show', compact('local', 'stats'));
    }

    /**
     * Show form to edit local.
     */
    public function editLocal(Local $local)
    {
        $this->authorize('update', $local);
        return view('seller.locals.edit', compact('local'));
    }

    /**
     * Update local.
     */
    public function updateLocal(Request $request, Local $local)
    {
        $this->authorize('update', $local);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|string|max:100',
            'location' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'equipment' => 'nullable|array',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',

            // Pricing fields
            'pricing_type' => 'required|in:fixed,flexible',
            'price' => 'required_if:pricing_type,fixed|nullable|numeric|min:0',
            'hourly_price' => 'required_if:pricing_type,flexible|nullable|numeric|min:0',
            'daily_price' => 'nullable|numeric|min:0',
            'weekly_price' => 'nullable|numeric|min:0',
            'monthly_price' => 'nullable|numeric|min:0',
            'yearly_price' => 'nullable|numeric|min:0',

            // Availability fields
            'minimum_rental_hours' => 'required|integer|min:1',
            'maximum_rental_hours' => 'nullable|integer|min:1',
            'available_24_7' => 'boolean',
            'available_hours' => 'nullable|array',
            'rental_terms' => 'nullable|string',
            'approval_required' => 'required|in:automatic,manual',
            'status' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($local->image) {
                Storage::disk('public')->delete($local->image);
            }
            $validated['image'] = $request->file('image')->store('locals', 'public');
        }

        $local->update($validated);

        return redirect()->route('seller.locals.show', $local)
            ->with('success', 'Local updated successfully!');
    }

    /**
     * Delete local.
     */
    public function destroyLocal(Local $local)
    {
        $this->authorize('delete', $local);

        // Check if there are active reservations
        $activeReservations = $local->reservations()
            ->whereIn('status', ['en_attente', 'confirmée'])
            ->count();

        if ($activeReservations > 0) {
            return back()->with('error', 'Cannot delete local with active reservations.');
        }

        // Delete image
        if ($local->image) {
            Storage::disk('public')->delete($local->image);
        }

        $local->delete();

        return redirect()->route('seller.locals')
            ->with('success', 'Local deleted successfully!');
    }

    /**
     * Display seller's reservations.
     */
    public function reservations()
    {
        $reservations = Reservation::whereHas('local', function ($query) {
            $query->where('seller_id', Auth::id());
        })->with(['local', 'user'])->latest()->paginate(15);

        return view('seller.reservations.index', compact('reservations'));
    }

    /**
     * Update reservation status.
     */
    public function updateReservationStatus(Request $request, Reservation $reservation)
    {
        // Check if user owns the local
        if ($reservation->local->seller_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'status' => 'required|in:en_attente,confirmée,annulée,terminée'
        ]);

        $reservation->update($validated);

        return back()->with('success', 'Reservation status updated successfully!');
    }

    /**
     * Get pending reservations for seller.
     */
    private function getPendingReservations($seller)
    {
        return Reservation::whereHas('local', function ($query) use ($seller) {
            $query->where('seller_id', $seller->id);
        })->where('status', 'en_attente');
    }

    /**
     * Get monthly earnings for seller.
     */
    private function getMonthlyEarnings($seller)
    {
        $reservations = Reservation::whereHas('local', function ($query) use ($seller) {
            $query->where('seller_id', $seller->id);
        })
        ->where('status', 'confirmée')
        ->whereMonth('created_at', now()->month)
        ->whereYear('created_at', now()->year)
        ->with('local')
        ->get();

        return $reservations->sum(function ($reservation) {
            return $reservation->calculateAmount();
        });
    }

    /**
     * Get recent reservations for seller.
     */
    private function getRecentReservations($seller)
    {
        return Reservation::whereHas('local', function ($query) use ($seller) {
            $query->where('seller_id', $seller->id);
        })->with(['local', 'user'])->latest()->limit(5)->get();
    }

    /**
     * Get top performing locals for seller.
     */
    private function getTopPerformingLocals($seller)
    {
        return $seller->locals()
            ->withCount('reservations')
            ->orderBy('reservations_count', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Get statistics for a specific local.
     */
    private function getLocalStats($local)
    {
        $confirmedReservations = $local->reservations()->where('status', 'confirmée')->with('local')->get();
        $totalEarnings = $confirmedReservations->sum(function ($reservation) {
            return $reservation->calculateAmount();
        });

        return [
            'total_reservations' => $local->reservations()->count(),
            'confirmed_reservations' => $local->reservations()->where('status', 'confirmée')->count(),
            'pending_reservations' => $local->reservations()->where('status', 'en_attente')->count(),
            'total_earnings' => $totalEarnings,
            'average_rating' => 4.5, // Placeholder for future rating system
        ];
    }
}
