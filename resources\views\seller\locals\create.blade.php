@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Tableau de bord vendeur</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.locals') }}">
                            <i class="fas fa-building"></i> Mes locaux
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('seller.locals.create') }}">
                            <i class="fas fa-plus"></i> Ajouter un nouveau local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.reservations') }}">
                            <i class="fas fa-calendar-check"></i> Réservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Ajouter un nouveau local</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('seller.locals') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour aux locaux
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="mb-0">Informations du local</h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('seller.locals.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf

                                <!-- Informations de base -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Nom du local *</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="type">Type *</label>
                                            <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                                                <option value="">Sélectionner le type</option>
                                                <option value="Office" {{ old('type') == 'Office' ? 'selected' : '' }}>Bureau</option>
                                                <option value="Meeting Room" {{ old('type') == 'Meeting Room' ? 'selected' : '' }}>Salle de réunion</option>
                                                <option value="Event Space" {{ old('type') == 'Event Space' ? 'selected' : '' }}>Espace événementiel</option>
                                                <option value="Coworking Space" {{ old('type') == 'Coworking Space' ? 'selected' : '' }}>Espace de coworking</option>
                                                <option value="Studio" {{ old('type') == 'Studio' ? 'selected' : '' }}>Studio</option>
                                                <option value="Warehouse" {{ old('type') == 'Warehouse' ? 'selected' : '' }}>Entrepôt</option>
                                                <option value="Other" {{ old('type') == 'Other' ? 'selected' : '' }}>Autre</option>
                                            </select>
                                            @error('type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description *</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="location">Localisation *</label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror"
                                                   id="location" name="location" value="{{ old('location') }}" required>
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="capacity">Capacité (personnes) *</label>
                                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1" required>
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Pricing Section -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Pricing Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Pricing Type *</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="fixed_pricing" value="fixed" {{ old('pricing_type', 'fixed') == 'fixed' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fixed_pricing">
                                                    Fixed Price (per booking)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="flexible_pricing" value="flexible" {{ old('pricing_type') == 'flexible' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="flexible_pricing">
                                                    Flexible Pricing (hourly, daily, weekly, monthly, yearly)
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Fixed Pricing -->
                                        <div id="fixed_price_section" class="pricing-section">
                                            <div class="form-group">
                                                <label for="price">Fixed Price (MAD) *</label>
                                                <input type="number" class="form-control @error('price') is-invalid @enderror"
                                                       id="price" name="price" value="{{ old('price') }}" step="0.01" min="0">
                                                @error('price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <!-- Flexible Pricing -->
                                        <div id="flexible_price_section" class="pricing-section" style="display: none;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="hourly_price">Hourly Price (MAD) *</label>
                                                        <input type="number" class="form-control @error('hourly_price') is-invalid @enderror"
                                                               id="hourly_price" name="hourly_price" value="{{ old('hourly_price') }}" step="0.01" min="0">
                                                        @error('hourly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="daily_price">Daily Price (MAD)</label>
                                                        <input type="number" class="form-control @error('daily_price') is-invalid @enderror"
                                                               id="daily_price" name="daily_price" value="{{ old('daily_price') }}" step="0.01" min="0">
                                                        @error('daily_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="weekly_price">Weekly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('weekly_price') is-invalid @enderror"
                                                               id="weekly_price" name="weekly_price" value="{{ old('weekly_price') }}" step="0.01" min="0">
                                                        @error('weekly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="monthly_price">Monthly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('monthly_price') is-invalid @enderror"
                                                               id="monthly_price" name="monthly_price" value="{{ old('monthly_price') }}" step="0.01" min="0">
                                                        @error('monthly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="yearly_price">Yearly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('yearly_price') is-invalid @enderror"
                                                               id="yearly_price" name="yearly_price" value="{{ old('yearly_price') }}" step="0.01" min="0">
                                                        @error('yearly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Availability Section -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Availability Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="minimum_rental_hours">Minimum Rental Hours *</label>
                                                    <input type="number" class="form-control @error('minimum_rental_hours') is-invalid @enderror"
                                                           id="minimum_rental_hours" name="minimum_rental_hours" value="{{ old('minimum_rental_hours', 1) }}" min="1" required>
                                                    @error('minimum_rental_hours')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="maximum_rental_hours">Maximum Rental Hours</label>
                                                    <input type="number" class="form-control @error('maximum_rental_hours') is-invalid @enderror"
                                                           id="maximum_rental_hours" name="maximum_rental_hours" value="{{ old('maximum_rental_hours') }}" min="1">
                                                    <small class="form-text text-muted">Leave empty for no limit</small>
                                                    @error('maximum_rental_hours')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="available_24_7" name="available_24_7" value="1" {{ old('available_24_7') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="available_24_7">
                                                    Available 24/7
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="approval_required">Booking Approval *</label>
                                            <select class="form-control @error('approval_required') is-invalid @enderror" id="approval_required" name="approval_required" required>
                                                <option value="manual" {{ old('approval_required', 'manual') == 'manual' ? 'selected' : '' }}>Manual Approval Required</option>
                                                <option value="automatic" {{ old('approval_required') == 'automatic' ? 'selected' : '' }}>Automatic Approval</option>
                                            </select>
                                            @error('approval_required')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Equipment and Image -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="equipment">Equipment Available</label>
                                            <div class="equipment-checkboxes">
                                                @php
                                                    $equipmentOptions = ['WiFi', 'Projector', 'Whiteboard', 'Air Conditioning', 'Parking', 'Kitchen', 'Printer', 'Sound System'];
                                                @endphp
                                                @foreach($equipmentOptions as $equipment)
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="{{ $equipment }}" id="equipment_{{ $loop->index }}"
                                                               {{ in_array($equipment, old('equipment', [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="equipment_{{ $loop->index }}">
                                                            {{ $equipment }}
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="image">Local Image</label>
                                            <input type="file" class="form-control-file @error('image') is-invalid @enderror"
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">Upload an image of your local (max 2MB)</small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="rental_terms">Rental Terms & Conditions</label>
                                    <textarea class="form-control @error('rental_terms') is-invalid @enderror"
                                              id="rental_terms" name="rental_terms" rows="3">{{ old('rental_terms') }}</textarea>
                                    @error('rental_terms')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group text-right">
                                    <button type="button" class="btn btn-secondary mr-2" onclick="history.back()">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Create Local</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">💡 Tips for Success</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>High-quality photos</strong> increase bookings by 40%
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Detailed descriptions</strong> help customers understand your space
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Competitive pricing</strong> attracts more customers
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Flexible availability</strong> increases booking opportunities
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Quick responses</strong> to inquiries build trust
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">📋 Pricing Guidelines</h6>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted">
                                <strong>Flexible Pricing Benefits:</strong><br>
                                • Hourly: Great for short meetings<br>
                                • Daily: Perfect for workshops<br>
                                • Weekly: Ideal for projects<br>
                                • Monthly: Best for long-term clients<br>
                                • Yearly: Maximum value for permanent setups
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fixedRadio = document.getElementById('fixed_pricing');
    const flexibleRadio = document.getElementById('flexible_pricing');
    const fixedSection = document.getElementById('fixed_price_section');
    const flexibleSection = document.getElementById('flexible_price_section');

    function togglePricingSections() {
        if (fixedRadio.checked) {
            fixedSection.style.display = 'block';
            flexibleSection.style.display = 'none';
        } else {
            fixedSection.style.display = 'none';
            flexibleSection.style.display = 'block';
        }
    }

    fixedRadio.addEventListener('change', togglePricingSections);
    flexibleRadio.addEventListener('change', togglePricingSections);

    // Initialize on page load
    togglePricingSections();
});
</script>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link:hover {
    color: #007bff;
}

main {
    margin-left: 16.66667%;
}

.equipment-checkboxes {
    max-height: 200px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
    }

    main {
        margin-left: 0;
    }
}
</style>
@endsection
