@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar bg-gradient-primary">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                    <span><i class="fas fa-store me-2"></i>Tableau de bord vendeur</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="{{ route('seller.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="{{ route('seller.locals') }}">
                            <i class="fas fa-building me-2"></i> Me<PERSON> locaux
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active bg-white text-primary rounded mx-2" href="{{ route('seller.locals.create') }}">
                            <i class="fas fa-plus me-2"></i> Ajouter un nouveau local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="{{ route('seller.reservations') }}">
                            <i class="fas fa-calendar-check me-2"></i> Réservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4 bg-light min-vh-100">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                <div>
                    <h1 class="h2 text-primary mb-1">
                        <i class="fas fa-plus-circle me-2"></i>Ajouter un nouveau local
                    </h1>
                    <p class="text-muted">Créez un nouveau local pour votre activité de location</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('seller.locals') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i> Retour aux locaux
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0 rounded-3">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations du local</h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('seller.locals.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf

                                <!-- Informations de base -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Nom du local *</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="type">Type *</label>
                                            <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                                                <option value="">Sélectionner le type</option>
                                                @foreach(\App\Models\LocalType::active()->ordered()->get() as $localType)
                                                    <option value="{{ $localType->name }}"
                                                            {{ old('type') == $localType->name ? 'selected' : '' }}
                                                            data-icon="{{ $localType->icon }}"
                                                            data-color="{{ $localType->color }}">
                                                        {{ $localType->display_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description">Description *</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                              id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="location">Localisation *</label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror"
                                                   id="location" name="location" value="{{ old('location') }}" required>
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="capacity">Capacité (personnes) *</label>
                                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1" required>
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Pricing Section -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Pricing Configuration</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Pricing Type *</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="fixed_pricing" value="fixed" {{ old('pricing_type', 'fixed') == 'fixed' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fixed_pricing">
                                                    Fixed Price (per booking)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="pricing_type" id="flexible_pricing" value="flexible" {{ old('pricing_type') == 'flexible' ? 'checked' : '' }}>
                                                <label class="form-check-label" for="flexible_pricing">
                                                    Flexible Pricing (hourly, daily, weekly, monthly, yearly)
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Fixed Pricing -->
                                        <div id="fixed_price_section" class="pricing-section">
                                            <div class="form-group">
                                                <label for="price">Fixed Price (MAD) *</label>
                                                <input type="number" class="form-control @error('price') is-invalid @enderror"
                                                       id="price" name="price" value="{{ old('price') }}" step="0.01" min="0">
                                                @error('price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <!-- Flexible Pricing -->
                                        <div id="flexible_price_section" class="pricing-section" style="display: none;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="hourly_price">Hourly Price (MAD) *</label>
                                                        <input type="number" class="form-control @error('hourly_price') is-invalid @enderror"
                                                               id="hourly_price" name="hourly_price" value="{{ old('hourly_price') }}" step="0.01" min="0">
                                                        @error('hourly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="daily_price">Daily Price (MAD)</label>
                                                        <input type="number" class="form-control @error('daily_price') is-invalid @enderror"
                                                               id="daily_price" name="daily_price" value="{{ old('daily_price') }}" step="0.01" min="0">
                                                        @error('daily_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="weekly_price">Weekly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('weekly_price') is-invalid @enderror"
                                                               id="weekly_price" name="weekly_price" value="{{ old('weekly_price') }}" step="0.01" min="0">
                                                        @error('weekly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="monthly_price">Monthly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('monthly_price') is-invalid @enderror"
                                                               id="monthly_price" name="monthly_price" value="{{ old('monthly_price') }}" step="0.01" min="0">
                                                        @error('monthly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="yearly_price">Yearly Price (MAD)</label>
                                                        <input type="number" class="form-control @error('yearly_price') is-invalid @enderror"
                                                               id="yearly_price" name="yearly_price" value="{{ old('yearly_price') }}" step="0.01" min="0">
                                                        @error('yearly_price')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Availability Section -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Availability Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="minimum_rental_hours">Minimum Rental Hours *</label>
                                                    <input type="number" class="form-control @error('minimum_rental_hours') is-invalid @enderror"
                                                           id="minimum_rental_hours" name="minimum_rental_hours" value="{{ old('minimum_rental_hours', 1) }}" min="1" required>
                                                    @error('minimum_rental_hours')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="maximum_rental_hours">Maximum Rental Hours</label>
                                                    <input type="number" class="form-control @error('maximum_rental_hours') is-invalid @enderror"
                                                           id="maximum_rental_hours" name="maximum_rental_hours" value="{{ old('maximum_rental_hours') }}" min="1">
                                                    <small class="form-text text-muted">Leave empty for no limit</small>
                                                    @error('maximum_rental_hours')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="available_24_7" name="available_24_7" value="1" {{ old('available_24_7') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="available_24_7">
                                                    Available 24/7
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="approval_required">Booking Approval *</label>
                                            <select class="form-control @error('approval_required') is-invalid @enderror" id="approval_required" name="approval_required" required>
                                                <option value="manual" {{ old('approval_required', 'manual') == 'manual' ? 'selected' : '' }}>Manual Approval Required</option>
                                                <option value="automatic" {{ old('approval_required') == 'automatic' ? 'selected' : '' }}>Automatic Approval</option>
                                            </select>
                                            @error('approval_required')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Equipment and Image -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="equipment">Equipment Available</label>
                                            <div class="equipment-checkboxes">
                                                @php
                                                    $equipmentOptions = ['WiFi', 'Projector', 'Whiteboard', 'Air Conditioning', 'Parking', 'Kitchen', 'Printer', 'Sound System'];
                                                @endphp
                                                @foreach($equipmentOptions as $equipment)
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="{{ $equipment }}" id="equipment_{{ $loop->index }}"
                                                               {{ in_array($equipment, old('equipment', [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="equipment_{{ $loop->index }}">
                                                            {{ $equipment }}
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="image">Local Image</label>
                                            <input type="file" class="form-control-file @error('image') is-invalid @enderror"
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">Upload an image of your local (max 2MB)</small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="rental_terms">Rental Terms & Conditions</label>
                                    <textarea class="form-control @error('rental_terms') is-invalid @enderror"
                                              id="rental_terms" name="rental_terms" rows="3">{{ old('rental_terms') }}</textarea>
                                    @error('rental_terms')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group text-right">
                                    <button type="button" class="btn btn-secondary mr-2" onclick="history.back()">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Create Local</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">💡 Tips for Success</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>High-quality photos</strong> increase bookings by 40%
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Detailed descriptions</strong> help customers understand your space
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Competitive pricing</strong> attracts more customers
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Flexible availability</strong> increases booking opportunities
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Quick responses</strong> to inquiries build trust
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">📋 Pricing Guidelines</h6>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted">
                                <strong>Flexible Pricing Benefits:</strong><br>
                                • Hourly: Great for short meetings<br>
                                • Daily: Perfect for workshops<br>
                                • Weekly: Ideal for projects<br>
                                • Monthly: Best for long-term clients<br>
                                • Yearly: Maximum value for permanent setups
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fixedRadio = document.getElementById('fixed_pricing');
    const flexibleRadio = document.getElementById('flexible_pricing');
    const fixedSection = document.getElementById('fixed_price_section');
    const flexibleSection = document.getElementById('flexible_price_section');

    function togglePricingSections() {
        if (fixedRadio.checked) {
            fixedSection.style.display = 'block';
            flexibleSection.style.display = 'none';
        } else {
            fixedSection.style.display = 'none';
            flexibleSection.style.display = 'block';
        }
    }

    fixedRadio.addEventListener('change', togglePricingSections);
    flexibleRadio.addEventListener('change', togglePricingSections);

    // Initialize on page load
    togglePricingSections();
});
</script>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #007bff !important;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.hover-white:hover {
    color: white !important;
}

/* Main Content */
main {
    margin-left: 16.66667%;
}

/* Form Enhancements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* Equipment Checkboxes */
.equipment-checkboxes {
    max-height: 250px;
    overflow-y: auto;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background-color: #f8f9fa;
}

.equipment-checkboxes .form-check {
    margin-bottom: 0.75rem;
}

.equipment-checkboxes .form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Pricing Sections */
.pricing-section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 2px solid #e9ecef;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    transform: translateY(-2px);
}

/* Background Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        padding: 1rem 0;
    }

    main {
        margin-left: 0;
    }

    .card-body {
        padding: 1rem;
    }

    .pricing-section {
        padding: 1rem;
    }
}

/* Animation for form sections */
.pricing-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Select Styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}
</style>
@endsection
