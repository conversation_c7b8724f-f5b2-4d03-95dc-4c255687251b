<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to modify the enum column to include more types
        DB::statement("ALTER TABLE locals MODIFY COLUMN type ENUM('sport', 'conference', 'fête', 'Office', 'Meeting Room', 'Event Space', 'Coworking Space', 'Studio', 'Warehouse', 'Other') DEFAULT 'Other'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, update any new types to 'Other' to avoid constraint violations
        DB::table('locals')->whereNotIn('type', ['sport', 'conference', 'fête'])->update(['type' => 'fête']);
        
        // Then modify the enum back to original values
        DB::statement("ALTER TABLE locals MODIFY COLUMN type ENUM('sport', 'conference', 'fête')");
    }
};
