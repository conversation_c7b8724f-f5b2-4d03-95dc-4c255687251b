/* ===== IMPROVED UI STYLES - ICONS & PAGINATION ===== */

/* ===== ICON IMPROVEMENTS ===== */

/* Default icon sizes - more reasonable */
.fas, .far, .fab, .fal, .fad {
    font-size: 0.875rem; /* Smaller default size */
}

/* Specific icon size classes */
.icon-xs {
    font-size: 0.75rem;
}

.icon-sm {
    font-size: 0.875rem;
}

.icon-md {
    font-size: 1rem;
}

.icon-lg {
    font-size: 1.25rem;
}

.icon-xl {
    font-size: 1.5rem;
}

/* Button icons - smaller and better spaced */
.btn .fas, .btn .far, .btn .fab {
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.btn-sm .fas, .btn-sm .far, .btn-sm .fab {
    font-size: 0.75rem;
    margin-right: 0.375rem;
}

.btn-lg .fas, .btn-lg .far, .btn-lg .fab {
    font-size: 0.9rem;
    margin-right: 0.625rem;
}

/* Card header icons */
.card-header .fas, .card-header .far {
    font-size: 0.875rem;
    margin-right: 0.5rem;
}

/* Navbar icons */
.navbar .fas, .navbar .far {
    font-size: 0.875rem;
}

/* Footer icons */
.footer .fas, .footer .far {
    font-size: 0.875rem;
}

/* Alert icons */
.alert .fas, .alert .far {
    font-size: 0.875rem;
    margin-right: 0.5rem;
}

/* ===== PAGINATION IMPROVEMENTS ===== */

/* Modern pagination container */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
    background: rgba(248, 249, 250, 0.6);
    border-radius: 0.75rem;
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

/* Pagination navigation styling */
nav[aria-label="Pagination Navigation"] {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem 0;
}

nav[aria-label="Pagination Navigation"] .pagination {
    justify-content: center;
    margin: 0;
}

nav[aria-label="Pagination Navigation"]:hover .pagination {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Pagination base styles */
.pagination {
    margin: 0;
    padding: 0;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Page link improvements */
.pagination .page-link {
    border: none;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    background: transparent;
    transition: all 0.2s ease;
    margin: 0;
    border-radius: 0;
    text-decoration: none;
    min-width: 40px;
    text-align: center;
}

/* Page link hover */
.pagination .page-link:hover {
    background: #f8f9fa;
    color: #495057;
    transform: none;
    box-shadow: none;
}

/* Active page */
.pagination .page-item.active .page-link {
    background: #007bff;
    color: white;
    border-color: #007bff;
    box-shadow: none;
}

/* Disabled page */
.pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background: transparent;
    border-color: transparent;
}

/* First and last page links */
.pagination .page-item:first-child .page-link {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.pagination .page-item:last-child .page-link {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

/* Previous/Next buttons */
.pagination .page-link[aria-label="Previous"],
.pagination .page-link[aria-label="Next"] {
    padding: 0.5rem 0.875rem;
    font-weight: 600;
}

/* Pagination info text styling */
.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* ===== COMPACT PAGINATION FOR MOBILE ===== */
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
        min-width: 32px;
    }

    .pagination .page-link[aria-label="Previous"],
    .pagination .page-link[aria-label="Next"] {
        padding: 0.375rem 0.625rem;
    }

    /* Hide some page numbers on mobile */
    .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(.disabled) {
        display: none;
    }

    .pagination .page-item.active ~ .page-item:nth-child(-n+2),
    .pagination .page-item.active ~ .page-item:nth-last-child(-n+2) {
        display: inline-block;
    }
}

/* ===== BADGE IMPROVEMENTS ===== */
.badge {
    font-size: 0.7rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

.badge.badge-sm {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

/* ===== DROPDOWN IMPROVEMENTS ===== */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    transform: none;
}

.dropdown-item .fas, .dropdown-item .far {
    font-size: 0.8rem;
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* ===== FORM IMPROVEMENTS ===== */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ===== BUTTON IMPROVEMENTS ===== */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* ===== CARD IMPROVEMENTS ===== */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.02);
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
    .btn {
        padding: 0.5rem 0.875rem;
        font-size: 0.875rem;
    }

    .btn .fas, .btn .far {
        font-size: 0.75rem;
        margin-right: 0.375rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.875rem 1rem;
    }
}
