<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Local;
use App\Models\Reservation;
use App\Models\User;
use App\Models\LocalType;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Display the home page with dynamic content.
     */
    public function index()
    {
        // Get featured locals (latest or most popular)
        $featuredLocals = Local::active()
            ->with(['reservations' => function($query) {
                $query->where('status', 'confirmée');
            }])
            ->latest()
            ->limit(6)
            ->get();

        // Get statistics for the platform
        $stats = [
            'total_locals' => Local::active()->count(),
            'total_reservations' => Reservation::where('status', 'confirmée')->count(),
            'total_users' => User::where('role', 'client')->count(),
            'total_sellers' => User::where('role', 'seller')->count(),
        ];

        // Get locals by type with counts
        $localTypes = [
            'sport' => [
                'name' => 'Terrains de sport',
                'icon' => 'fas fa-futbol',
                'color' => 'success',
                'description' => 'Réservez des terrains pour vos matchs ou entraînements.',
                'count' => Local::active()->where('type', 'sport')->count(),
                'latest' => Local::active()->where('type', 'sport')->latest()->limit(3)->get()
            ],
            'conference' => [
                'name' => 'Salles de conférences',
                'icon' => 'fas fa-presentation-screen',
                'color' => 'primary',
                'description' => 'Idéales pour vos séminaires et présentations.',
                'count' => Local::active()->where('type', 'conference')->count(),
                'latest' => Local::active()->where('type', 'conference')->latest()->limit(3)->get()
            ],
            'fête' => [
                'name' => 'Salles de fêtes',
                'icon' => 'fas fa-glass-cheers',
                'color' => 'warning',
                'description' => 'Célébrez vos moments importants dans nos salles festives.',
                'count' => Local::active()->where('type', 'fête')->count(),
                'latest' => Local::active()->where('type', 'fête')->latest()->limit(3)->get()
            ]
        ];

        // Get dynamic types from database if LocalType model exists
        try {
            $dynamicTypes = LocalType::active()->ordered()->get();
            if ($dynamicTypes->count() > 0) {
                $localTypes = [];
                foreach ($dynamicTypes as $type) {
                    $localTypes[$type->name] = [
                        'name' => $type->display_name,
                        'icon' => $type->icon ?? 'fas fa-building',
                        'color' => $type->color ?? 'primary',
                        'description' => $type->description ?? "Découvrez nos {$type->display_name}",
                        'count' => Local::active()->where('type', $type->name)->count(),
                        'latest' => Local::active()->where('type', $type->name)->latest()->limit(3)->get()
                    ];
                }
            }
        } catch (\Exception $e) {
            // Continue with static types if LocalType doesn't exist
        }

        // Get recent activity (latest reservations)
        $recentActivity = Reservation::with(['local', 'user'])
            ->where('status', 'confirmée')
            ->latest()
            ->limit(5)
            ->get();

        // Get popular locals (most reserved)
        $popularLocals = Local::active()
            ->withCount(['reservations' => function($query) {
                $query->where('status', 'confirmée');
            }])
            ->orderBy('reservations_count', 'desc')
            ->limit(4)
            ->get();

        // Get pricing ranges
        $pricingRanges = [
            'min_price' => Local::active()->min('price') ?? 0,
            'max_price' => Local::active()->max('price') ?? 1000,
            'avg_price' => Local::active()->avg('price') ?? 100,
        ];

        // Get cities with locals
        $cities = Local::active()
            ->select('location', DB::raw('count(*) as count'))
            ->groupBy('location')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        return view('welcome', compact(
            'featuredLocals',
            'stats',
            'localTypes',
            'recentActivity',
            'popularLocals',
            'pricingRanges',
            'cities'
        ));
    }
}
