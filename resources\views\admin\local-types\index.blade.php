@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Gestion des types de locaux
                    </h4>
                    <a href="{{ route('admin.local-types.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Ajouter un type
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Ordre</th>
                                    <th>Nom</th>
                                    <th>Nom FR</th>
                                    <th>Icône</th>
                                    <th>Couleur</th>
                                    <th>Locaux</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($types as $type)
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">{{ $type->sort_order }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ $type->name }}</strong>
                                            @if($type->description)
                                                <br><small class="text-muted">{{ $type->description }}</small>
                                            @endif
                                        </td>
                                        <td>{{ $type->name_fr ?? '-' }}</td>
                                        <td>
                                            <i class="{{ $type->icon }}" style="color: {{ $type->color }}; font-size: 1.2rem;"></i>
                                            <small class="d-block text-muted">{{ $type->icon }}</small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="color-preview me-2" style="width: 20px; height: 20px; background-color: {{ $type->color }}; border-radius: 3px; border: 1px solid #ddd;"></div>
                                                <code>{{ $type->color }}</code>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $type->locals_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <form action="{{ route('admin.local-types.toggle-status', $type) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="btn btn-sm {{ $type->active ? 'btn-success' : 'btn-secondary' }}">
                                                    <i class="fas {{ $type->active ? 'fa-check' : 'fa-times' }}"></i>
                                                    {{ $type->active ? 'Actif' : 'Inactif' }}
                                                </button>
                                            </form>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.local-types.edit', $type) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($type->locals_count == 0)
                                                    <form action="{{ route('admin.local-types.destroy', $type) }}" method="POST" class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce type ?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @else
                                                    <button class="btn btn-sm btn-outline-secondary" disabled title="Impossible de supprimer - {{ $type->locals_count }} locaux utilisent ce type">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Aucun type de local trouvé.</p>
                                            <a href="{{ route('admin.local-types.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Créer le premier type
                                            </a>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.color-preview {
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
@endpush
