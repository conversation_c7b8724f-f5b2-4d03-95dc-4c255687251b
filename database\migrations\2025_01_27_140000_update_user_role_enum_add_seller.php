<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to modify the enum column to include 'seller'
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('client', 'admin', 'seller') DEFAULT 'client'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, update any 'seller' roles to 'client' to avoid constraint violations
        DB::table('users')->where('role', 'seller')->update(['role' => 'client']);
        
        // Then modify the enum back to original values
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('client', 'admin') DEFAULT 'client'");
    }
};
