@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user me-2"></i>Profil utilisateur
                    </h1>
                    <p class="text-muted">Détails et activité de {{ $user->name }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.users') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                    @if($user->id !== auth()->id())
                        <button type="button" class="btn btn-outline-warning me-2" onclick="editUser({{ $user->id }})">
                            <i class="fas fa-edit me-2"></i>Modifier
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteUser({{ $user->id }})">
                            <i class="fas fa-trash me-2"></i>Supprimer
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; background: #007bff; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user text-white fa-2x"></i>
                        </div>
                        <h4 class="mb-1">{{ $user->name }}</h4>
                        @if($user->role === 'admin')
                            <span class="badge bg-danger">
                                <i class="fas fa-user-shield me-1"></i>Administrateur
                            </span>
                        @else
                            <span class="badge bg-primary">
                                <i class="fas fa-user me-1"></i>Client
                            </span>
                        @endif
                    </div>

                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <strong>Email</strong><br>
                            <span class="text-muted">{{ $user->email }}</span>
                        </div>
                        <div class="col-6">
                            <strong>Inscription</strong><br>
                            <span class="text-muted">{{ $user->created_at->format('d/m/Y') }}</span>
                        </div>
                        <div class="col-6">
                            <strong>Dernière connexion</strong><br>
                            <span class="text-muted">
                                {{ $user->updated_at ? $user->updated_at->format('d/m/Y') : 'Jamais' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border-bottom pb-2 mb-2">
                                <h3 class="text-primary mb-0">{{ $userStats['total_reservations'] }}</h3>
                                <small class="text-muted">Total réservations</small>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="border-bottom pb-2 mb-2">
                                <h3 class="text-success mb-0">{{ $userStats['confirmed_reservations'] }}</h3>
                                <small class="text-muted">Confirmées</small>
                            </div>
                        </div>
                        <div class="col-12">
                            <h3 class="text-warning mb-0">{{ number_format(abs($userStats['total_spent']), 0) }} MAD</h3>
                            <small class="text-muted">Total dépensé</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reservations List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Historique des réservations
                    </h5>
                </div>
                <div class="card-body">
                    @if($user->reservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->reservations->sortByDesc('created_at') as $reservation)
                                    <tr>
                                        <td>
                                            <strong>#{{ $reservation->id }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $reservation->local->name }}</strong><br>
                                                <small class="text-muted">{{ $reservation->local->location }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {{ \Carbon\Carbon::parse($reservation->date)->format('d/m/Y') }}
                                        </td>
                                        <td>
                                            {{ \Carbon\Carbon::parse($reservation->start_time)->format('H:i') }} - 
                                            {{ \Carbon\Carbon::parse($reservation->end_time)->format('H:i') }}
                                        </td>
                                        <td>
                                            @if($reservation->invoice)
                                                <strong>{{ number_format(abs($reservation->invoice->amount), 0) }} MAD</strong>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($reservation->status === 'confirmée')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>Confirmée
                                                </span>
                                            @elseif($reservation->status === 'en attente')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>En attente
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle me-1"></i>Annulée
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-outline-primary" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($reservation->invoice)
                                                    <a href="{{ route('invoices.show', $reservation->invoice) }}" class="btn btn-outline-success" title="Facture">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réservation trouvée</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>Modifier utilisateur
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label for="editName" class="form-label">Nom complet</label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Rôle</label>
                        <select class="form-select" id="editRole" name="role" required>
                            <option value="client">Client</option>
                            <option value="admin">Administrateur</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">Nouveau mot de passe (optionnel)</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                        <small class="text-muted">Laissez vide pour conserver le mot de passe actuel</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Sauvegarder
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Fonction pour éditer un utilisateur
function editUser(userId) {
    fetch(`/admin/users/${userId}/edit`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('editUserId').value = data.user.id;
                document.getElementById('editName').value = data.user.name;
                document.getElementById('editEmail').value = data.user.email;
                document.getElementById('editRole').value = data.user.role;
                document.getElementById('editPassword').value = '';

                new bootstrap.Modal(document.getElementById('editUserModal')).show();
            } else {
                alert('Erreur lors du chargement des données utilisateur');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des données utilisateur');
        });
}

// Fonction pour supprimer un utilisateur
function deleteUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
        fetch(`/admin/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => window.location.href = '{{ route("admin.users") }}', 1500);
            } else {
                showNotification(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur lors de la suppression', 'error');
        });
    }
}

// Gestion du formulaire d'édition
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const userId = document.getElementById('editUserId').value;
    const formData = new FormData(this);

    fetch(`/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(Object.fromEntries(formData))
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la mise à jour', 'error');
    });
});

// Fonction pour afficher les notifications
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    const icon = type === 'success' ? 'check-circle' :
                type === 'error' ? 'exclamation-triangle' :
                type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
@endpush

@endsection
