<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'qr_code',
        'role',
        'is_active',
        'business_name',
        'business_license',
        'business_description',
        'phone',
        'address',
        'commission_rate',
        'verified_seller',
        'seller_approved_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'verified_seller' => 'boolean',
            'seller_approved_at' => 'datetime',
            'commission_rate' => 'decimal:2',
        ];
    }

    /**
     * Get the reservations for the user.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the notifications for the user.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is client.
     */
    public function isClient(): bool
    {
        return $this->role === 'client';
    }

    /**
     * Check if user is seller.
     */
    public function isSeller(): bool
    {
        return $this->role === 'seller';
    }

    /**
     * Check if user is verified seller.
     */
    public function isVerifiedSeller(): bool
    {
        return $this->isSeller() && $this->verified_seller;
    }

    /**
     * Get the locals owned by the seller.
     */
    public function locals(): HasMany
    {
        return $this->hasMany(Local::class, 'seller_id');
    }

    /**
     * Get active locals owned by the seller.
     */
    public function activeLocals(): HasMany
    {
        return $this->locals()->where('status', true);
    }

    /**
     * Calculate total earnings for seller.
     */
    public function totalEarnings()
    {
        return $this->locals()
            ->join('reservations', 'locals.id', '=', 'reservations.local_id')
            ->where('reservations.status', 'confirmée')
            ->sum('reservations.total_amount');
    }
}
