@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord administrateur
            </h1>
            <p class="text-muted">Vue d'ensemble de la plateforme LocaSpace</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['total_users'] }}</h4>
                            <p class="mb-0">Utilisateurs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-primary bg-opacity-75">
                    <a href="{{ route('admin.users') }}" class="text-white text-decoration-none">
                        <small>Voir tous <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['total_locals'] }}</h4>
                            <p class="mb-0">Locaux</p>
                            <small>({{ $stats['active_locals'] }} actifs)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-success bg-opacity-75">
                    <a href="{{ route('admin.locals.index') }}" class="text-white text-decoration-none">
                        <small>Gérer <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $stats['total_reservations'] }}</h4>
                            <p class="mb-0">Réservations</p>
                            <small>({{ $stats['pending_reservations'] }} en attente)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-info bg-opacity-75">
                    <a href="{{ route('admin.reservations') }}" class="text-white text-decoration-none">
                        <small>Voir toutes <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ number_format(abs($stats['total_revenue']), 0) }} MAD</h4>
                            <p class="mb-0">Revenus</p>
                            <small>({{ number_format(abs($stats['pending_payments']), 0) }} MAD en attente)</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-warning bg-opacity-75">
                    <a href="{{ route('admin.invoices.index') }}" class="text-white text-decoration-none">
                        <small>Voir factures <i class="fas fa-arrow-right ms-1"></i></small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Reservations -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Réservations récentes
                        </h5>
                        <a href="{{ route('admin.reservations') }}" class="btn btn-sm btn-outline-primary">
                            Voir toutes
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($recentReservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentReservations as $reservation)
                                    <tr>
                                        <td>
                                            <strong>{{ $reservation->user->name }}</strong><br>
                                            <small class="text-muted">{{ $reservation->user->email }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ $reservation->local->name }}</strong><br>
                                            <small class="text-muted">{{ $reservation->local->location }}</small>
                                        </td>
                                        <td>
                                            {{ $reservation->date->format('d/m/Y') }}<br>
                                            <small class="text-muted">{{ $reservation->start_time }} - {{ $reservation->end_time }}</small>
                                        </td>
                                        <td>
                                            @if($reservation->status === 'confirmée')
                                                <span class="badge bg-success">Confirmée</span>
                                            @elseif($reservation->status === 'en attente')
                                                <span class="badge bg-warning">En attente</span>
                                            @else
                                                <span class="badge bg-danger">Annulée</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($reservation->invoice)
                                                {{ abs($reservation->invoice->amount) }} MAD
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('reservations.show', $reservation) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($reservation->status === 'en attente')
                                                    <form method="POST" action="{{ route('admin.reservations.confirm', $reservation) }}"
                                                          style="display: inline;">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réservation récente</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions & Charts -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.locals.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Ajouter un local
                        </a>
                        <a href="{{ route('admin.reservations', ['status' => 'en attente']) }}" class="btn btn-warning">
                            <i class="fas fa-clock me-2"></i>Réservations en attente
                        </a>
                        <a href="{{ route('admin.reports') }}" class="btn btn-info">
                            <i class="fas fa-chart-bar me-2"></i>Voir les rapports
                        </a>
                        <a href="{{ route('admin.users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-users me-2"></i>Gérer les utilisateurs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Monthly Revenue Chart -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Revenus mensuels
                    </h5>
                </div>
                <div class="card-body">
                    @if($monthlyRevenue->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th class="text-end">Revenus</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($monthlyRevenue->take(6) as $revenue)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($revenue->year . '-' . $revenue->month . '-01')->format('M Y') }}</td>
                                        <td class="text-end">{{ number_format(abs($revenue->total), 0) }} MAD</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('admin.reports') }}" class="btn btn-sm btn-outline-primary">
                                Voir le rapport complet
                            </a>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Aucune donnée disponible</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>État du système
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                <h6>Système</h6>
                                <small class="text-success">Opérationnel</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-database text-success fa-2x mb-2"></i>
                                <h6>Base de données</h6>
                                <small class="text-success">Connectée</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <i class="fas fa-envelope text-success fa-2x mb-2"></i>
                                <h6>Email</h6>
                                <small class="text-success">Fonctionnel</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-credit-card text-success fa-2x mb-2"></i>
                            <h6>Paiements</h6>
                            <small class="text-success">Actifs</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
