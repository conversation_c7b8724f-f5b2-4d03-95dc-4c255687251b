<!-- Navigation Bar Component -->
<nav class="navbar navbar-expand-lg navbar-dark shadow-lg fixed-top"
    style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand fw-bold fs-3 d-flex align-items-center" href="{{ route('home') }}"
            style="transition: transform 0.3s ease;">
            <div class="brand-logo me-2" style="background: rgba(255,255,255); padding: 6px; border-radius: 8px;">
                <img src="{{ asset('images/logo.png') }}" alt="LocaSpace Logo" style="height: 32px; width: auto;">
            </div>
        </a>


        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent"
            aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar Content -->
        <div class="collapse navbar-collapse" id="navbarContent">
            <!-- Left Navigation -->
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                        <i class="fas fa-home me-1"></i>
                        <span>Accueil</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('locals.*') ? 'active' : '' }}"
                        href="{{ route('locals.index') }}">
                        <i class="fas fa-search me-1"></i>
                        <span>Locaux</span>
                    </a>
                </li>




                @auth
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
                            href="{{ route('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('reservations.*') ? 'active' : '' }}"
                            href="{{ route('reservations.index') }}">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span>Réservations</span>
                        </a>
                    </li>

                    @if(Auth::user()->isAdmin())
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.*') ? 'active' : '' }}" href="#"
                                id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cogs me-1"></i>
                                <span>Admin</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern" aria-labelledby="adminDropdown">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-chart-bar me-2"></i>Tableau de bord
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                        <i class="fas fa-chart-line me-2"></i>Dashboard Admin
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-cogs me-2"></i>Gestion
                                    </h6>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('locals.index') }}">
                                        <i class="fas fa-building me-2"></i>Locaux
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.reservations') }}">
                                        <i class="fas fa-calendar-check me-2"></i>Réservations
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.users') }}">
                                        <i class="fas fa-users me-2"></i>Utilisateurs
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.reports') }}">
                                        <i class="fas fa-chart-pie me-2"></i>Rapports
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endif
                @endauth
            </ul>

            <!-- Right Navigation -->
            <ul class="navbar-nav ms-auto">
                @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('qr.login') }}">
                            <i class="fas fa-qrcode me-1"></i>
                            <span>QR Login</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span>Connexion</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-sm ms-2 px-3" href="{{ route('register') }}">
                            <i class="fas fa-user-plus me-1"></i>
                            <span>Inscription</span>
                        </a>
                    </li>
                @else
                    <!-- Notifications -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell fs-5"></i>
                            <span id="notificationBadge"
                                class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-pulse"
                                style="display: none;">
                                0
                                <span class="visually-hidden">notifications non lues</span>
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern notification-dropdown"
                            aria-labelledby="notificationDropdown" style="min-width: 350px;">
                            <li>
                                <div class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-bell me-2"></i>Notifications</span>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="markAllReadBtn"
                                        style="display: none;">
                                        <i class="fas fa-check-double me-1"></i>Tout marquer lu
                                    </button>
                                </div>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <div id="notificationsList">
                                <li class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin me-2"></i>Chargement...
                                </li>
                            </div>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item text-center" href="#" id="viewAllNotifications">
                                    <small><i class="fas fa-eye me-1"></i>Voir toutes les notifications</small>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-dropdown" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-info d-none d-md-block">
                                    <span class="user-name">{{ Auth::user()->name }}</span>
                                    @if(Auth::user()->isAdmin())
                                        <span class="user-badge">Admin</span>
                                    @endif
                                </div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-modern user-menu"
                            aria-labelledby="userDropdown">
                            <li>
                                <div class="dropdown-header user-header">
                                    <div class="user-avatar-large">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-details">
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <small class="text-muted">{{ Auth::user()->email }}</small>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('profile.show') }}">
                                    <i class="fas fa-user me-2"></i>Mon profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('reservations.index') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                                </a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @endguest
            </ul>
        </div>
    </div>
</nav>

<!-- Spacer for fixed navbar -->
<div class="navbar-spacer"></div>

<style>
    /* Navbar Brand Hover Effect */
    .navbar-brand:hover {
        transform: scale(1.05) !important;
    }

    /* Nav Links Hover Effects */
    .nav-link {
        position: relative;
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        margin: 0 4px !important;
    }

    .nav-link:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        transform: translateY(-2px) !important;
    }

    .nav-link.active {
        background: rgba(255, 255, 255, 0.25) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    /* Dropdown Improvements */
    .dropdown-menu {
        border: none !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
        border-radius: 12px !important;
        backdrop-filter: blur(10px) !important;
        background: rgba(255, 255, 255, 0.95) !important;
    }

    .dropdown-item {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        margin: 2px 8px !important;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        transform: translateX(5px) !important;
    }

    /* User Avatar */
    .user-avatar {
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .user-dropdown:hover .user-avatar {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    /* Notification Badge Animation */
    .notification-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(1);
        }

        50% {
            transform: translate(-50%, -50%) scale(1.1);
        }

        100% {
            transform: translate(-50%, -50%) scale(1);
        }
    }

    /* Mobile Improvements */
    @media (max-width: 991.98px) {
        .nav-link:hover {
            transform: none !important;
            background: rgba(255, 255, 255, 0.1) !important;
        }

        .navbar-brand:hover {
            transform: none !important;
        }
    }

    /* Glassmorphism Effect */
    .navbar {
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
    }

    /* Notification Styles */
    .notification-item {
        padding: 12px 16px !important;
        border-left: 3px solid transparent;
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        background: rgba(0, 123, 255, 0.1) !important;
        border-left-color: #007bff;
    }

    .notification-item.unread {
        background: rgba(0, 123, 255, 0.05) !important;
        border-left-color: #007bff;
    }

    .notification-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 4px;
        color: #333;
    }

    .notification-text {
        font-size: 0.8rem;
        margin-bottom: 4px;
        color: #666;
        line-height: 1.3;
    }

    .notification-time {
        font-size: 0.75rem;
        color: #999;
    }

    .notification-dropdown {
        max-height: 400px;
        overflow-y: auto;
    }

    /* Logo hover effect */
    .brand-logo:hover img {
        transform: scale(1.1);
        transition: transform 0.3s ease;
    }
</style>

<script>
    // Notifications dynamiques
    document.addEventListener('DOMContentLoaded', function () {
        let notificationDropdown = document.getElementById('notificationDropdown');
        let notificationBadge = document.getElementById('notificationBadge');
        let notificationsList = document.getElementById('notificationsList');
        let markAllReadBtn = document.getElementById('markAllReadBtn');

        // Charger les notifications au clic sur le dropdown
        notificationDropdown.addEventListener('click', function () {
            loadNotifications();
        });

        // Marquer toutes comme lues
        markAllReadBtn.addEventListener('click', function () {
            markAllNotificationsAsRead();
        });

        // Charger les notifications initiales
        loadNotifications();

        // Actualiser toutes les 30 secondes
        setInterval(loadNotifications, 30000);

        function loadNotifications() {
            fetch('/api/notifications', {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    updateNotificationBadge(data.unread_count);
                    updateNotificationsList(data.notifications);
                    updateMarkAllButton(data.unread_count);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des notifications:', error);
                    notificationsList.innerHTML = '<li class="text-center py-3 text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erreur de chargement</li>';
                });
        }

        function updateNotificationBadge(count) {
            if (count > 0) {
                notificationBadge.textContent = count > 99 ? '99+' : count;
                notificationBadge.style.display = 'inline-block';
            } else {
                notificationBadge.style.display = 'none';
            }
        }

        function updateNotificationsList(notifications) {
            if (notifications.length === 0) {
                notificationsList.innerHTML = '<li class="text-center py-3 text-muted"><i class="fas fa-bell-slash me-2"></i>Aucune notification</li>';
                return;
            }

            let html = '';
            notifications.forEach(notification => {
                html += `
                <li>
                    <a class="dropdown-item notification-item unread" href="#" onclick="markAsRead(${notification.id})">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="${notification.icon} ${notification.color} fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="notification-title">${notification.content}</h6>
                                <small class="notification-time">${notification.created_at}</small>
                            </div>
                        </div>
                    </a>
                </li>
            `;
            });

            notificationsList.innerHTML = html;
        }

        function updateMarkAllButton(count) {
            if (count > 0) {
                markAllReadBtn.style.display = 'inline-block';
            } else {
                markAllReadBtn.style.display = 'none';
            }
        }

        function markAllNotificationsAsRead() {
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadNotifications();
                        showNotification('Toutes les notifications ont été marquées comme lues', 'success');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur lors de la mise à jour', 'error');
                });
        }

        // Fonction globale pour marquer une notification comme lue
        window.markAsRead = function (notificationId) {
            fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadNotifications();
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                });
        };

        // Fonction pour afficher des notifications toast
        window.showNotification = function (message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' :
                type === 'error' ? 'alert-danger' :
                    type === 'warning' ? 'alert-warning' : 'alert-info';
            const icon = type === 'success' ? 'check-circle' :
                type === 'error' ? 'exclamation-triangle' :
                    type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
            notification.innerHTML = `
            <i class="fas fa-${icon} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        };
    });
</script>