@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Seller Dashboard</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('seller.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.locals') }}">
                            <i class="fas fa-building"></i> My Locals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.locals.create') }}">
                            <i class="fas fa-plus"></i> Add New Local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('seller.reservations') }}">
                            <i class="fas fa-calendar-check"></i> Reservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Seller Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <a href="{{ route('seller.locals.create') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-plus"></i> Add New Local
                        </a>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Locals
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_locals'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-building fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Active Locals
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_locals'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Total Reservations
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_reservations'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Total Earnings
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_earnings'], 2) }} MAD</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <!-- Recent Reservations -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Reservations</h6>
                        </div>
                        <div class="card-body">
                            @if($recentReservations->count() > 0)
                                @foreach($recentReservations as $reservation)
                                    <div class="d-flex align-items-center border-bottom py-2">
                                        <div class="mr-3">
                                            <div class="icon-circle bg-primary">
                                                <i class="fas fa-calendar text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="small text-gray-500">{{ $reservation->created_at->format('M d, Y') }}</div>
                                            <div class="font-weight-bold">{{ $reservation->local->name }}</div>
                                            <div class="small">by {{ $reservation->user->name }}</div>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-{{ $reservation->status === 'confirmée' ? 'success' : ($reservation->status === 'en_attente' ? 'warning' : 'secondary') }}">
                                                {{ ucfirst($reservation->status) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted">No recent reservations.</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Top Performing Locals -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Top Performing Locals</h6>
                        </div>
                        <div class="card-body">
                            @if($topLocals->count() > 0)
                                @foreach($topLocals as $local)
                                    <div class="d-flex align-items-center border-bottom py-2">
                                        <div class="mr-3">
                                            @if($local->image)
                                                <img src="{{ Storage::url($local->image) }}" alt="{{ $local->name }}" class="rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="icon-circle bg-success">
                                                    <i class="fas fa-building text-white"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="font-weight-bold">{{ $local->name }}</div>
                                            <div class="small text-gray-500">{{ $local->type }} • {{ $local->location }}</div>
                                        </div>
                                        <div class="text-right">
                                            <div class="font-weight-bold">{{ $local->reservations_count }}</div>
                                            <div class="small text-gray-500">reservations</div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <p class="text-muted">No locals available.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('seller.locals.create') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> Add New Local
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('seller.locals') }}" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-building"></i> Manage Locals
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('seller.reservations') }}" class="btn btn-outline-success btn-block">
                                        <i class="fas fa-calendar-check"></i> View Reservations
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('profile.edit') }}" class="btn btn-outline-secondary btn-block">
                                        <i class="fas fa-user-cog"></i> Profile Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link:hover {
    color: #007bff;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

main {
    margin-left: 16.66667%;
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
    }
    
    main {
        margin-left: 0;
    }
}
</style>
@endsection
