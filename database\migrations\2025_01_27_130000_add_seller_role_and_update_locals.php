<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add seller_id to locals table and pricing structure
        Schema::table('locals', function (Blueprint $table) {
            $table->foreignId('seller_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->json('pricing_structure')->nullable(); // Store different pricing options
            $table->enum('pricing_type', ['fixed', 'flexible'])->default('fixed');
            $table->decimal('hourly_price', 10, 2)->nullable();
            $table->decimal('daily_price', 10, 2)->nullable();
            $table->decimal('weekly_price', 10, 2)->nullable();
            $table->decimal('monthly_price', 10, 2)->nullable();
            $table->decimal('yearly_price', 10, 2)->nullable();
            $table->integer('minimum_rental_hours')->default(1);
            $table->integer('maximum_rental_hours')->nullable();
            $table->boolean('available_24_7')->default(false);
            $table->json('available_hours')->nullable(); // Store available time slots
            $table->text('rental_terms')->nullable();
            $table->enum('approval_required', ['automatic', 'manual'])->default('manual');
        });

        // Update user roles to include seller
        Schema::table('users', function (Blueprint $table) {
            // Add seller-specific fields
            $table->string('business_name')->nullable();
            $table->string('business_license')->nullable();
            $table->text('business_description')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->decimal('commission_rate', 5, 2)->default(10.00); // Platform commission
            $table->boolean('verified_seller')->default(false);
            $table->timestamp('seller_approved_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('locals', function (Blueprint $table) {
            $table->dropForeign(['seller_id']);
            $table->dropColumn([
                'seller_id',
                'pricing_structure',
                'pricing_type',
                'hourly_price',
                'daily_price',
                'weekly_price',
                'monthly_price',
                'yearly_price',
                'minimum_rental_hours',
                'maximum_rental_hours',
                'available_24_7',
                'available_hours',
                'rental_terms',
                'approval_required'
            ]);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'business_name',
                'business_license',
                'business_description',
                'phone',
                'address',
                'commission_rate',
                'verified_seller',
                'seller_approved_at'
            ]);
        });
    }
};
