@extends('layouts.auth')

@section('title', 'Connexion')

@section('content')
<div class="auth-card">
    <div class="auth-header">
        <div class="logo-container">
            <i class="fas fa-building"></i>
        </div>
        <h1 class="auth-title">Bon retour !</h1>
        <p class="auth-subtitle">Connectez-vous pour accéder à vos réservations</p>
    </div>

    <div class="auth-body">
        <!-- Flash Messages -->
        @if(session('error'))
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            </div>
        @endif

        <form method="POST" action="{{ route('login') }}">
            @csrf

            <!-- Email -->
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>Adresse email
                </label>
                <input type="email"
                       class="form-control @error('email') is-invalid @enderror"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       placeholder="<EMAIL>"
                       required
                       autocomplete="email"
                       autofocus>
                @error('email')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password -->
            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i>Mot de passe
                </label>
                <input type="password"
                       class="form-control @error('password') is-invalid @enderror"
                       id="password"
                       name="password"
                       placeholder="••••••••"
                       required
                       autocomplete="current-password">
                @error('password')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Se souvenir de moi
                    </label>
                </div>
                <a href="#" class="auth-footer">
                    Mot de passe oublié ?
                </a>
            </div>

            <!-- Submit Button -->
            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                </button>
            </div>
        </form>

        <!-- Divider -->
        <div class="divider">
            <span>Ou connectez-vous avec</span>
        </div>

        <!-- Alternative Login Methods -->
        <div class="d-grid mb-4">
            <a href="{{ route('qr.login') }}" class="btn btn-outline-primary">
                <i class="fas fa-qrcode me-2"></i>Scanner QR Code
            </a>
        </div>

        <!-- QR Code Info -->
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-3"></i>
                <div>
                    <strong>Connexion rapide :</strong> Utilisez votre QR code personnel pour vous connecter instantanément !
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <p>
            Pas encore de compte ?
            <a href="{{ route('register') }}">Créer un compte</a>
        </p>
    </div>
</div>
@endsection
