<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('reservations.index')); ?>">Mes réservations</a></li>
            <li class="breadcrumb-item active">Réservation #<?php echo e($reservation->id); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>Détails de la réservation
                        </h4>
                        <div>
                            <?php if($reservation->status === 'confirmée'): ?>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check me-1"></i>Confirmée
                                </span>
                            <?php elseif($reservation->status === 'en attente'): ?>
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-clock me-1"></i>En attente
                                </span>
                            <?php else: ?>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times me-1"></i>Annulée
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Reservation Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle text-primary me-2"></i>Informations de réservation</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Numéro :</strong></td>
                                    <td>#<?php echo e($reservation->id); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date :</strong></td>
                                    <td><?php echo e($reservation->date->format('d/m/Y')); ?> (<?php echo e($reservation->date->format('l')); ?>)</td>
                                </tr>
                                <tr>
                                    <td><strong>Heure :</strong></td>
                                    <td><?php echo e($reservation->start_time); ?> - <?php echo e($reservation->end_time); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Durée :</strong></td>
                                    <td><?php echo e(abs($reservation->getDurationInHours())); ?> heure(s)</td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le :</strong></td>
                                    <td><?php echo e($reservation->created_at->format('d/m/Y à H:i')); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-building text-primary me-2"></i>Local réservé</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <?php if($reservation->local->type === 'sport'): ?>
                                            <i class="fas fa-futbol text-success fa-2x me-3"></i>
                                        <?php elseif($reservation->local->type === 'conference'): ?>
                                            <i class="fas fa-presentation-screen text-primary fa-2x me-3"></i>
                                        <?php else: ?>
                                            <i class="fas fa-glass-cheers text-warning fa-2x me-3"></i>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($reservation->local->name); ?></h6>
                                            <small class="text-muted"><?php echo e(ucfirst($reservation->local->type)); ?></small>
                                        </div>
                                    </div>
                                    <p class="mb-2">
                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                        <?php echo e($reservation->local->location); ?>

                                    </p>
                                    <p class="mb-2">
                                        <i class="fas fa-users text-muted me-2"></i>
                                        Capacité : <?php echo e($reservation->local->capacity); ?> personnes
                                    </p>
                                    <p class="mb-0">
                                        <i class="fas fa-coins text-muted me-2"></i>
                                        Prix : <?php echo e(abs($reservation->local->price)); ?> MAD/heure
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Equipment -->
                    <?php if($reservation->local->equipment && count($reservation->local->equipment) > 0): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5><i class="fas fa-cogs text-primary me-2"></i>Équipements inclus</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $reservation->local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-light text-dark border">
                                        <?php if($equipment === 'wifi'): ?>
                                            <i class="fas fa-wifi me-1"></i>WiFi
                                        <?php elseif($equipment === 'projecteur'): ?>
                                            <i class="fas fa-video me-1"></i>Projecteur
                                        <?php elseif($equipment === 'climatisation'): ?>
                                            <i class="fas fa-snowflake me-1"></i>Climatisation
                                        <?php else: ?>
                                            <i class="fas fa-check me-1"></i><?php echo e($equipment); ?>

                                        <?php endif; ?>
                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?php echo e(route('reservations.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Retour à mes réservations
                                </a>

                                <div>
                                    <?php if(in_array($reservation->status, ['en attente', 'confirmée'])): ?>
                                        <form method="POST" action="<?php echo e(route('reservations.cancel', $reservation)); ?>"
                                              style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit"
                                                    class="btn btn-outline-danger"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')">
                                                <i class="fas fa-times me-2"></i>Annuler la réservation
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Invoice Card -->
            <?php if($reservation->invoice): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>Facture
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h4 class="text-success"><?php echo e(abs($reservation->invoice->amount)); ?> MAD</h4>
                        <p class="text-muted mb-0">Montant total</p>
                    </div>

                    <div class="mb-3">
                        <strong>Statut du paiement :</strong><br>
                        <?php if($reservation->invoice->isPaid()): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Payé
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-circle me-1"></i>Non payé
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <?php if($reservation->invoice->isPaid()): ?>
                            <a href="<?php echo e(route('invoices.show', $reservation->invoice)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <a href="<?php echo e(route('invoices.download', $reservation->invoice)); ?>" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Télécharger PDF
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('invoices.show', $reservation->invoice)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir la facture
                            </a>
                            <button type="button" class="btn btn-warning w-100" id="payButton"
                                    data-invoice-id="<?php echo e($reservation->invoice->id); ?>"
                                    data-amount="<?php echo e($reservation->invoice->amount); ?>">
                                <i class="fas fa-credit-card me-2"></i>Payer maintenant
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Contact Support -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>Besoin d'aide ?
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        Une question concernant votre réservation ? Notre équipe est là pour vous aider.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Contacter le support
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reservation Timeline -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation créée</h6>
                                <small class="text-muted"><?php echo e($reservation->created_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>

                        <?php if($reservation->status === 'confirmée'): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation confirmée</h6>
                                <small class="text-muted"><?php echo e($reservation->updated_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>
                        <?php elseif($reservation->status === 'annulée'): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Réservation annulée</h6>
                                <small class="text-muted"><?php echo e($reservation->updated_at->format('d/m/Y à H:i')); ?></small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dee2e6;
}

.payment-loading {
    display: none;
}

.payment-success {
    display: none;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const payButton = document.getElementById('payButton');

    if (payButton) {
        payButton.addEventListener('click', function() {
            const invoiceId = this.dataset.invoiceId;
            const amount = this.dataset.amount;

            console.log('Payment button clicked:', { invoiceId, amount });

            // Vérifier que les données sont présentes
            if (!invoiceId || !amount) {
                alert('Erreur: Données de paiement manquantes');
                return;
            }

            // Vérifier que Stripe est chargé
            if (typeof Stripe === 'undefined') {
                alert('Erreur: Stripe n\'est pas chargé');
                return;
            }

            // Afficher le loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
            this.disabled = true;

            console.log('Sending payment request to API...');

            // Créer le Payment Intent
            fetch('/api/stripe/create-payment-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    invoice_id: invoiceId,
                    amount: amount
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('Réponse API:', data);

                if (data.success) {
                    console.log('Payment session created successfully');

                    // Si c'est une session de test
                    if (data.session_id && data.session_id.startsWith('test_session_')) {
                        alert('Test réussi ! Session ID: ' + data.session_id + '\nDébug: ' + JSON.stringify(data.debug, null, 2));

                        // Restaurer le bouton
                        payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Payer maintenant';
                        payButton.disabled = false;
                        return;
                    }

                    // Vérifier que session_id existe
                    if (!data.session_id) {
                        throw new Error('Session ID manquant dans la réponse');
                    }

                    // Initialiser Stripe pour une vraie session
                    console.log('Initializing Stripe with key:', '<?php echo e(config("services.stripe.key")); ?>');
                    const stripe = Stripe('<?php echo e(config("services.stripe.key")); ?>');

                    console.log('Redirecting to Stripe Checkout...');
                    // Rediriger vers Stripe Checkout
                    return stripe.redirectToCheckout({
                        sessionId: data.session_id
                    });
                } else {
                    console.error('API returned error:', data);
                    throw new Error(data.error || 'Erreur lors de la création du paiement');
                }
            })
            .then(result => {
                if (result.error) {
                    throw new Error(result.error.message);
                }
            })
            .catch(error => {
                console.error('Erreur de paiement:', error);

                // Restaurer le bouton
                payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Payer maintenant';
                payButton.disabled = false;

                // Afficher l'erreur détaillée
                let errorMessage = 'Erreur lors du paiement: ' + error.message;
                if (error.stack) {
                    console.error('Stack trace:', error.stack);
                }

                alert(errorMessage + '\n\nVérifiez la console pour plus de détails.');
            });
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/reservations/show.blade.php ENDPATH**/ ?>