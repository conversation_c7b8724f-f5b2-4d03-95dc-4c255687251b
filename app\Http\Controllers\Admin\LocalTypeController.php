<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LocalType;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class LocalTypeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Display a listing of local types.
     */
    public function index()
    {
        $types = LocalType::withCount('locals')->ordered()->paginate(15);
        return view('admin.local-types.index', compact('types'));
    }

    /**
     * Show the form for creating a new local type.
     */
    public function create()
    {
        return view('admin.local-types.create');
    }

    /**
     * Store a newly created local type.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:local_types,name',
            'name_fr' => 'nullable|string|max:100',
            'name_ar' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:255',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = LocalType::max('sort_order') + 1;
        }

        LocalType::create($validated);

        return redirect()->route('admin.local-types.index')
            ->with('success', 'Type de local créé avec succès.');
    }

    /**
     * Show the form for editing the specified local type.
     */
    public function edit(LocalType $localType)
    {
        return view('admin.local-types.edit', compact('localType'));
    }

    /**
     * Update the specified local type.
     */
    public function update(Request $request, LocalType $localType)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:100', Rule::unique('local_types', 'name')->ignore($localType->id)],
            'name_fr' => 'nullable|string|max:100',
            'name_ar' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:255',
            'icon' => 'required|string|max:50',
            'color' => 'required|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $localType->update($validated);

        return redirect()->route('admin.local-types.index')
            ->with('success', 'Type de local mis à jour avec succès.');
    }

    /**
     * Remove the specified local type.
     */
    public function destroy(LocalType $localType)
    {
        // Check if any locals are using this type
        $localsCount = $localType->locals()->count();

        if ($localsCount > 0) {
            return back()->with('error', "Impossible de supprimer ce type. {$localsCount} locaux l'utilisent encore.");
        }

        $localType->delete();

        return redirect()->route('admin.local-types.index')
            ->with('success', 'Type de local supprimé avec succès.');
    }

    /**
     * Toggle the active status of a local type.
     */
    public function toggleStatus(LocalType $localType)
    {
        $localType->update(['active' => !$localType->active]);

        $status = $localType->active ? 'activé' : 'désactivé';
        return back()->with('success', "Type de local {$status} avec succès.");
    }
}
