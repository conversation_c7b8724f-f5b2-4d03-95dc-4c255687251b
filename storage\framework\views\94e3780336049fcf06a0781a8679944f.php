<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item active">Rapports</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Rapports et Analytics
                    </h1>
                    <p class="text-muted">Vue d'ensemble des performances de la plateforme</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue by Month -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Revenus par mois (12 derniers mois)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($revenueByMonth->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th class="text-end">Revenus (MAD)</th>
                                        <th class="text-end">Évolution</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $revenueByMonth; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $revenue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e(\Carbon\Carbon::parse($revenue->month . '-01')->format('F Y')); ?></strong>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success fs-6"><?php echo e(number_format(abs($revenue->total), 2)); ?> MAD</span>
                                        </td>
                                        <td class="text-end">
                                            <?php if($index < $revenueByMonth->count() - 1): ?>
                                                <?php
                                                    $previousRevenue = $revenueByMonth[$index + 1]->total;
                                                    $currentRevenue = $revenue->total;
                                                    $evolution = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
                                                ?>
                                                <?php if($evolution > 0): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-arrow-up"></i> +<?php echo e(number_format($evolution, 1)); ?>%
                                                    </span>
                                                <?php elseif($evolution < 0): ?>
                                                    <span class="text-danger">
                                                        <i class="fas fa-arrow-down"></i> <?php echo e(number_format($evolution, 1)); ?>%
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="fas fa-minus"></i> 0%
                                                    </span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <th>Total</th>
                                        <th class="text-end"><?php echo e(number_format(abs($revenueByMonth->sum('total')), 2)); ?> MAD</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée de revenus disponible</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Reservations by Type -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Réservations par type de local
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($reservationsByType->count() > 0): ?>
                        <?php $totalReservations = $reservationsByType->sum('count'); ?>
                        <?php $__currentLoopData = $reservationsByType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <?php if($type->type === 'sport'): ?>
                                    <i class="fas fa-futbol text-success fa-2x me-3"></i>
                                <?php elseif($type->type === 'conference'): ?>
                                    <i class="fas fa-presentation-screen text-primary fa-2x me-3"></i>
                                <?php else: ?>
                                    <i class="fas fa-glass-cheers text-warning fa-2x me-3"></i>
                                <?php endif; ?>
                                <div>
                                    <h6 class="mb-0"><?php echo e(ucfirst($type->type)); ?></h6>
                                    <small class="text-muted"><?php echo e($type->count); ?> réservations</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary fs-6"><?php echo e($type->count); ?></span>
                                <br>
                                <small class="text-muted">
                                    <?php echo e($totalReservations > 0 ? number_format(($type->count / $totalReservations) * 100, 1) : 0); ?>%
                                </small>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total</strong>
                            <strong><?php echo e($totalReservations); ?> réservations</strong>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réservation confirmée</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Top Locals by Revenue -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>Top locaux par revenus
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($topLocalsByRevenue->count() > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $topLocalsByRevenue; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $local): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <?php if($index === 0): ?>
                                            <i class="fas fa-trophy text-warning fa-lg"></i>
                                        <?php elseif($index === 1): ?>
                                            <i class="fas fa-medal text-secondary fa-lg"></i>
                                        <?php elseif($index === 2): ?>
                                            <i class="fas fa-award text-warning fa-lg"></i>
                                        <?php else: ?>
                                            <span class="badge bg-light text-dark"><?php echo e($index + 1); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($local->name); ?></h6>
                                        <small class="text-muted"><?php echo e($local->location); ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <?php if($local->type === 'sport'): ?>
                                        <i class="fas fa-futbol text-success me-2"></i>
                                    <?php elseif($local->type === 'conference'): ?>
                                        <i class="fas fa-presentation-screen text-primary me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-glass-cheers text-warning me-2"></i>
                                    <?php endif; ?>
                                    <div>
                                        <span class="badge bg-success"><?php echo e(number_format(abs($local->total_revenue), 0)); ?> MAD</span>
                                        <br>
                                        <small class="text-muted"><?php echo e(abs($local->price)); ?> MAD/h</small>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun local avec revenus</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Résumé des performances
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-primary"><?php echo e($revenueByMonth->count()); ?></h3>
                                <p class="text-muted mb-0">Mois analysés</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-success"><?php echo e(number_format(abs($revenueByMonth->sum('total')), 0)); ?> MAD</h3>
                                <p class="text-muted mb-0">Revenus totaux</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-info"><?php echo e($reservationsByType->sum('count')); ?></h3>
                                <p class="text-muted mb-0">Réservations confirmées</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-warning"><?php echo e($topLocalsByRevenue->count()); ?></h3>
                            <p class="text-muted mb-0">Locaux performants</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .breadcrumb, .navbar {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }

    .card-header {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/admin/reports.blade.php ENDPATH**/ ?>