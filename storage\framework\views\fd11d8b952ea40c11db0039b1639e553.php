<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar bg-gradient-primary">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                    <span><i class="fas fa-store me-2"></i>Tableau de bord vendeur</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active bg-white text-primary rounded mx-2" href="<?php echo e(route('seller.locals')); ?>">
                            <i class="fas fa-building me-2"></i> Mes locaux
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.locals.create')); ?>">
                            <i class="fas fa-plus me-2"></i> Ajouter un nouveau local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.reservations')); ?>">
                            <i class="fas fa-calendar-check me-2"></i> Réservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4 bg-light min-vh-100">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                <div>
                    <h1 class="h2 text-primary mb-1">
                        <i class="fas fa-building me-2"></i><?php echo e($local->name); ?>

                    </h1>
                    <p class="text-muted">Détails et statistiques de votre local</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo e(route('seller.locals')); ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i> Retour aux locaux
                    </a>
                    <a href="<?php echo e(route('seller.locals.edit', $local)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> Modifier
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Local Details -->
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0 rounded-3 mb-4">
                        <div class="card-header bg-gradient-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations du local</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <?php if($local->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $local->image)); ?>" 
                                             alt="<?php echo e($local->name); ?>" 
                                             class="img-fluid rounded shadow-sm mb-3"
                                             style="max-height: 300px; width: 100%; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" style="height: 300px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h4 class="text-primary mb-3"><?php echo e($local->name); ?></h4>
                                    <p class="text-muted mb-3"><?php echo e($local->description); ?></p>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4"><strong>Type :</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-primary"><?php echo e(ucfirst($local->type)); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4"><strong>Localisation :</strong></div>
                                        <div class="col-sm-8"><?php echo e($local->location); ?></div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4"><strong>Capacité :</strong></div>
                                        <div class="col-sm-8"><?php echo e($local->capacity); ?> personnes</div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4"><strong>Statut :</strong></div>
                                        <div class="col-sm-8">
                                            <?php if($local->status): ?>
                                                <span class="badge bg-success">Actif</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactif</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="card shadow-lg border-0 rounded-3 mb-4">
                        <div class="card-header bg-gradient-success text-white">
                            <h5 class="mb-0"><i class="fas fa-euro-sign me-2"></i>Tarification</h5>
                        </div>
                        <div class="card-body">
                            <?php if($local->pricing_type === 'flexible'): ?>
                                <h6 class="text-success mb-3">Tarification flexible</h6>
                                <div class="row">
                                    <?php if($local->hourly_price): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>Prix horaire :</span>
                                                <strong><?php echo e($local->hourly_price); ?> MAD/h</strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($local->daily_price): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>Prix journalier :</span>
                                                <strong><?php echo e($local->daily_price); ?> MAD/jour</strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($local->weekly_price): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>Prix hebdomadaire :</span>
                                                <strong><?php echo e($local->weekly_price); ?> MAD/semaine</strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($local->monthly_price): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>Prix mensuel :</span>
                                                <strong><?php echo e($local->monthly_price); ?> MAD/mois</strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($local->yearly_price): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>Prix annuel :</span>
                                                <strong><?php echo e($local->yearly_price); ?> MAD/an</strong>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <h6 class="text-primary mb-3">Prix fixe</h6>
                                <div class="text-center">
                                    <h3 class="text-primary"><?php echo e($local->price); ?> MAD</h3>
                                    <p class="text-muted">par réservation</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Equipment -->
                    <?php if($local->equipment && count($local->equipment) > 0): ?>
                    <div class="card shadow-lg border-0 rounded-3 mb-4">
                        <div class="card-header bg-gradient-info text-white">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Équipements disponibles</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-2">
                                        <span class="badge bg-light text-dark me-1">
                                            <i class="fas fa-check text-success me-1"></i><?php echo e($equipment); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Statistics Sidebar -->
                <div class="col-lg-4">
                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo e($stats['total_reservations']); ?></h3>
                                    <p class="mb-0">Réservations totales</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo e($stats['confirmed_reservations']); ?></h3>
                                    <p class="mb-0">Réservations confirmées</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo e($stats['pending_reservations']); ?></h3>
                                    <p class="mb-0">En attente</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo e(number_format($stats['total_earnings'], 2)); ?> MAD</h3>
                                    <p class="mb-0">Revenus totaux</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Reservations -->
                    <div class="card shadow-lg border-0 rounded-3">
                        <div class="card-header bg-gradient-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-calendar me-2"></i>Réservations récentes</h6>
                        </div>
                        <div class="card-body">
                            <?php if($local->reservations->count() > 0): ?>
                                <?php $__currentLoopData = $local->reservations->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reservation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                                        <div>
                                            <strong><?php echo e($reservation->user->name); ?></strong><br>
                                            <small class="text-muted"><?php echo e($reservation->date->format('d/m/Y')); ?></small>
                                        </div>
                                        <span class="badge 
                                            <?php if($reservation->status === 'confirmée'): ?> bg-success
                                            <?php elseif($reservation->status === 'en_attente'): ?> bg-warning
                                            <?php else: ?> bg-danger
                                            <?php endif; ?>">
                                            <?php echo e(ucfirst($reservation->status)); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <p class="text-muted text-center">Aucune réservation pour le moment</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #007bff !important;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.hover-white:hover {
    color: white !important;
}

/* Main Content */
main {
    margin-left: 16.66667%;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* Background Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        padding: 1rem 0;
    }

    main {
        margin-left: 0;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/seller/locals/show.blade.php ENDPATH**/ ?>