<?php

namespace App\Helpers;

class FileExporterHelper
{
 public static function exportAsCSV($data, $filename = 'export.csv')
{
    if (empty($filename) || empty($data)) {
        return;
    }

    header('Content-Type: text/csv');
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $output = fopen('php://output', 'w');

    // Write the header
    fputcsv($output, array_keys(reset($data)));

    // Write rows
    foreach ($data as $row) {
        fputcsv($output, $row);
    }

    fclose($output);
    
}

  
}
