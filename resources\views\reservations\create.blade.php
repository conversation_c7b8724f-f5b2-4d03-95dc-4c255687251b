@extends('layouts.app')

@push('styles')
<style>
/* Step-based Design */
.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    font-weight: bold;
    margin-right: 10px;
}

/* Duration Type Selection Cards */
.duration-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.duration-option:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.duration-option.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #f8f9ff 0%, #e7f3ff 100%);
    box-shadow: 0 4px 12px rgba(0,123,255,0.2);
}

/* Pricing Cards */
.pricing-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.pricing-card:hover {
    border-color: #28a745;
    box-shadow: 0 4px 12px rgba(40,167,69,0.15);
    transform: translateY(-2px);
}

.pricing-card.active {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff8 0%, #e7f7e7 100%);
    box-shadow: 0 4px 12px rgba(40,167,69,0.2);
}

/* Time Sections */
.time-section {
    animation: fadeIn 0.3s ease-in;
}

/* Preset Time Buttons */
.preset-btn {
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preset-btn:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.preset-btn.active {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

/* Summary Section */
.reservation-summary .row {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.reservation-summary .row:last-child {
    border-bottom: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .step-number {
        width: 25px;
        height: 25px;
        line-height: 25px;
        font-size: 0.9rem;
    }

    .preset-btn {
        height: 60px;
        font-size: 0.9rem;
    }
}
</style>
@endpush

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Display validation errors -->
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Display existing pending reservations warning -->
                    @if($existingPendingReservations->count() > 0)
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-clock me-2"></i>Réservations en attente</h6>
                            <p class="mb-2">Vous avez déjà {{ $existingPendingReservations->count() }} réservation(s) en attente pour ce local :</p>
                            <ul class="mb-2">
                                @foreach($existingPendingReservations as $reservation)
                                    <li>
                                        <strong>{{ $reservation->date->format('d/m/Y') }}</strong>
                                        de {{ $reservation->start_time->format('H:i') }}
                                        à {{ $reservation->end_time->format('H:i') }}
                                        <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye me-1"></i>Voir
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Vous ne pouvez pas créer une nouvelle réservation pour la même date tant qu'une réservation est en attente.
                                Veuillez finaliser ou annuler vos réservations existantes.
                            </small>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="@if($local->pricing_type === 'flexible')hourly @else fixed @endif">
                        <input type="hidden" name="duration_count" id="duration_count" value="1">
                        <input type="hidden" name="duration_unit" id="duration_unit" value="@if($local->pricing_type === 'flexible')hours @else fixed @endif">

                        <!-- Step 1: Date & Time Selection -->
                        <div class="card mb-4 border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    <span class="step-number">1</span> Choisissez votre date et horaire
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Date Selection -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="date" class="form-label fw-bold">
                                            <i class="fas fa-calendar me-1 text-primary"></i>Date de réservation
                                        </label>
                                        <input type="date"
                                               class="form-control form-control-lg @error('date') is-invalid @enderror"
                                               id="date"
                                               name="date"
                                               value="{{ old('date', request('date', now()->addDay()->format('Y-m-d'))) }}"
                                               min="{{ now()->format('Y-m-d') }}"
                                               required>
                                        @error('date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-info-circle me-1 text-info"></i>Disponibilité
                                        </label>
                                        <div class="border rounded p-3 bg-light">
                                            <div id="dateAvailabilityInfo" class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Sélectionnez une date pour voir la disponibilité
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Duration Type Selection -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-clock me-1 text-primary"></i>Type de réservation
                                        </label>
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <div class="card h-100 duration-option" data-type="specific">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                                                        <h6 class="card-title">Créneaux spécifiques</h6>
                                                        <p class="card-text small text-muted">Choisissez vos heures de début et fin</p>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="duration_type" id="specific_time" value="specific" checked>
                                                            <label class="form-check-label" for="specific_time">Sélectionner</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="card h-100 duration-option" data-type="all_day">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-sun fa-2x text-warning mb-2"></i>
                                                        <h6 class="card-title">Toute la journée</h6>
                                                        <p class="card-text small text-muted">24 heures (00:00 - 23:59)</p>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="duration_type" id="all_day_radio" value="all_day">
                                                            <label class="form-check-label" for="all_day_radio">Sélectionner</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="card h-100 duration-option" data-type="preset">
                                                    <div class="card-body text-center p-3">
                                                        <i class="fas fa-bolt fa-2x text-success mb-2"></i>
                                                        <h6 class="card-title">Créneaux rapides</h6>
                                                        <p class="card-text small text-muted">Matin, après-midi, soirée</p>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="duration_type" id="preset_time" value="preset">
                                                            <label class="form-check-label" for="preset_time">Sélectionner</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Specific Time Selection -->
                                <div id="specificTimeSection" class="time-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="start_time" class="form-label">
                                                <i class="fas fa-play me-1"></i>Heure de début
                                            </label>
                                            <input type="time"
                                                   class="form-control form-control-lg @error('start_time') is-invalid @enderror"
                                                   id="start_time"
                                                   name="start_time"
                                                   value="{{ old('start_time', request('start_time', '09:00')) }}">
                                            @error('start_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-6">
                                            <label for="end_time" class="form-label">
                                                <i class="fas fa-stop me-1"></i>Heure de fin
                                            </label>
                                            <input type="time"
                                                   class="form-control form-control-lg @error('end_time') is-invalid @enderror"
                                                   id="end_time"
                                                   name="end_time"
                                                   value="{{ old('end_time', request('end_time', '10:00')) }}">
                                            @error('end_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Preset Time Selection -->
                                <div id="presetTimeSection" class="time-section" style="display: none;">
                                    <div class="row g-2">
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-start="08:00" data-end="12:00">
                                                <i class="fas fa-sun me-1"></i>
                                                <div>Matin</div>
                                                <small>8h - 12h</small>
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-start="12:00" data-end="14:00">
                                                <i class="fas fa-utensils me-1"></i>
                                                <div>Midi</div>
                                                <small>12h - 14h</small>
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-start="14:00" data-end="18:00">
                                                <i class="fas fa-sun me-1"></i>
                                                <div>Après-midi</div>
                                                <small>14h - 18h</small>
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-start="18:00" data-end="22:00">
                                                <i class="fas fa-moon me-1"></i>
                                                <div>Soirée</div>
                                                <small>18h - 22h</small>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- All Day Hidden Inputs -->
                                <input type="hidden" name="all_day" id="all_day" value="0">
                            </div>
                        </div>

                        <!-- Step 2: Pricing & Duration -->
                        @if($local->pricing_type === 'flexible')
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-tags me-2"></i>
                                    <span class="step-number">2</span> Tarification et durée
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    @if($local->hourly_price)
                                    <div class="col-md-6">
                                        <div class="card pricing-card h-100" data-pricing="hourly">
                                            <div class="card-body text-center">
                                                <i class="fas fa-clock fa-2x text-primary mb-3"></i>
                                                <h5 class="card-title">{{ $local->hourly_price }} MAD</h5>
                                                <p class="text-muted">par heure</p>
                                                <small class="text-muted">Idéal pour réunions courtes</small>
                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="hourly_pricing" value="hourly" checked>
                                                    <label class="form-check-label" for="hourly_pricing">Choisir ce tarif</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    @if($local->daily_price && $local->daily_price > 0)
                                    <div class="col-md-6">
                                        <div class="card pricing-card h-100" data-pricing="daily">
                                            <div class="card-body text-center">
                                                <i class="fas fa-calendar-day fa-2x text-success mb-3"></i>
                                                <h5 class="card-title">{{ $local->daily_price }} MAD</h5>
                                                <p class="text-muted">par jour</p>
                                                <small class="text-muted">Parfait pour ateliers</small>
                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="daily_pricing" value="daily">
                                                    <label class="form-check-label" for="daily_pricing">Choisir ce tarif</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    @if($local->weekly_price && $local->weekly_price > 0)
                                    <div class="col-md-6">
                                        <div class="card pricing-card h-100" data-pricing="weekly">
                                            <div class="card-body text-center">
                                                <i class="fas fa-calendar-week fa-2x text-info mb-3"></i>
                                                <h5 class="card-title">{{ $local->weekly_price }} MAD</h5>
                                                <p class="text-muted">par semaine</p>
                                                <small class="text-muted">Idéal pour projets</small>
                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="weekly_pricing" value="weekly">
                                                    <label class="form-check-label" for="weekly_pricing">Choisir ce tarif</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    @if($local->monthly_price && $local->monthly_price > 0)
                                    <div class="col-md-6">
                                        <div class="card pricing-card h-100" data-pricing="monthly">
                                            <div class="card-body text-center">
                                                <i class="fas fa-calendar-alt fa-2x text-warning mb-3"></i>
                                                <h5 class="card-title">{{ $local->monthly_price }} MAD</h5>
                                                <p class="text-muted">par mois</p>
                                                <small class="text-muted">Pour locations longues</small>
                                                <div class="form-check mt-3">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="monthly_pricing" value="monthly">
                                                    <label class="form-check-label" for="monthly_pricing">Choisir ce tarif</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Step 3: Summary & Confirmation -->
                        <div class="card mb-4 border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-receipt me-2"></i>
                                    <span class="step-number">3</span> Récapitulatif de votre réservation
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="reservation-summary">
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <strong><i class="fas fa-calendar me-1 text-primary"></i>Date :</strong>
                                                </div>
                                                <div class="col-6">
                                                    <span id="summary-date">-</span>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <strong><i class="fas fa-clock me-1 text-primary"></i>Horaire :</strong>
                                                </div>
                                                <div class="col-6">
                                                    <span id="summary-time">-</span>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <strong><i class="fas fa-hourglass-half me-1 text-primary"></i>Durée :</strong>
                                                </div>
                                                <div class="col-6">
                                                    <span id="summary-duration">-</span>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-6">
                                                    <strong><i class="fas fa-tag me-1 text-primary"></i>Tarif :</strong>
                                                </div>
                                                <div class="col-6">
                                                    <span id="summary-pricing">-</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Total à payer</h6>
                                                <div class="display-6 text-success fw-bold" id="summary-total">
                                                    {{ $local->pricing_type === 'flexible' ? ($local->hourly_price ?? $local->price) : $local->price }} MAD
                                                </div>
                                                <small class="text-muted">TTC</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Availability Check -->
                                <div class="mt-4">
                                    <button type="button" class="btn btn-outline-primary" id="checkAvailability">
                                        <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                    </button>
                                    <div id="availabilityResult" class="mt-3"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Terms & Payment -->
                        <div class="card mb-4 border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-contract me-2"></i>
                                    <span class="step-number">4</span> Conditions et paiement
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" class="form-check-input" id="terms" required>
                                            <label class="form-check-label" for="terms">
                                                <strong>J'accepte les conditions</strong><br>
                                                <small class="text-muted">
                                                    En cochant cette case, j'accepte les
                                                    <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                                    et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                                </small>
                                            </label>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-1"></i>Informations importantes :</h6>
                                            <ul class="mb-0 small">
                                                <li>Paiement sécurisé en ligne</li>
                                                <li>Confirmation immédiate par email</li>
                                                <li>Facture PDF automatique</li>
                                                @if($local->approval_required === 'manual')
                                                    <li class="text-warning">⚠️ Approbation manuelle requise</li>
                                                @else
                                                    <li class="text-success">✅ Confirmation automatique</li>
                                                @endif
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="d-grid gap-2">
                                            <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-arrow-left me-2"></i>Retour au local
                                            </a>
                                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn" disabled>
                                                <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '{{ $local->pricing_type }}';
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }},
        yearly: {{ $local->yearly_price ?? 0 }}
    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        let rateText = '';

        switch(option) {
            case 'hourly':
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
                break;
            case 'daily':
                if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'weekly':
                if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'monthly':
                if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'yearly':
                if (prices.yearly > 0) {
                    rateText = `${prices.yearly} MAD/an`;
                } else if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois (tarif mensuel)`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'fixed':
                rateText = `${prices.fixed} MAD/réservation`;
                break;
            default:
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
        }

        currentRateSpan.textContent = rateText;
    }

    function calculateDurationAndCost() {
        const selectedOption = getSelectedPricingOption();
        console.log('Selected pricing option:', selectedOption); // Debug log
        console.log('Available prices:', prices); // Debug log

        let total = 0;
        let durationText = '';
        let billingUnit = '';

        if (pricingType === 'flexible') {
            // Calculate based on selected pricing option and user input
            switch(selectedOption) {
                case 'hourly':
                    const hoursCount = parseFloat(document.getElementById('hours_count').value) || 1;
                    if (prices.hourly > 0) {
                        total = hoursCount * prices.hourly;
                        durationText = hoursCount + ' heure' + (hoursCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h';
                        console.log('Hourly calculation:', hoursCount, 'x', prices.hourly, '=', total);
                    }
                    break;
                case 'daily':
                    const daysCount = parseInt(document.getElementById('days_count').value) || 1;
                    if (prices.daily > 0) {
                        total = daysCount * prices.daily;
                        durationText = daysCount + ' jour' + (daysCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour';
                        console.log('Daily calculation:', daysCount, 'x', prices.daily, '=', total);
                    } else {
                        // Fallback to hourly if daily not available
                        const hoursCount = daysCount * 24;
                        total = hoursCount * prices.hourly;
                        durationText = daysCount + ' jour' + (daysCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                        billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        console.log('Daily fallback to hourly:', hoursCount, 'x', prices.hourly, '=', total);
                    }
                    break;
                case 'weekly':
                    const weeksCount = parseInt(document.getElementById('weeks_count').value) || 1;
                    if (prices.weekly > 0) {
                        total = weeksCount * prices.weekly;
                        durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine';
                        console.log('Weekly calculation:', weeksCount, 'x', prices.weekly, '=', total);
                    } else {
                        // Fallback to daily or hourly
                        if (prices.daily > 0) {
                            const daysCount = weeksCount * 7;
                            total = daysCount * prices.daily;
                            durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '') + ' (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = weeksCount * 7 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Weekly fallback calculation:', total);
                    }
                    break;
                case 'monthly':
                    const monthsCount = parseInt(document.getElementById('months_count').value) || 1;
                    if (prices.monthly > 0) {
                        total = monthsCount * prices.monthly;
                        durationText = monthsCount + ' mois';
                        billingUnit = 'Facturation: ' + monthsCount + ' × ' + prices.monthly + ' MAD/mois';
                        console.log('Monthly calculation:', monthsCount, 'x', prices.monthly, '=', total);
                    } else {
                        // Fallback to weekly, daily, or hourly
                        if (prices.weekly > 0) {
                            const weeksCount = monthsCount * 4;
                            total = weeksCount * prices.weekly;
                            durationText = monthsCount + ' mois (' + weeksCount + ' semaines)';
                            billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                        } else if (prices.daily > 0) {
                            const daysCount = monthsCount * 30;
                            total = daysCount * prices.daily;
                            durationText = monthsCount + ' mois (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = monthsCount * 30 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = monthsCount + ' mois (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Monthly fallback calculation:', total);
                    }
                    break;
                case 'yearly':
                    const yearsCount = parseInt(document.getElementById('years_count').value) || 1;
                    if (prices.yearly > 0) {
                        total = yearsCount * prices.yearly;
                        durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + yearsCount + ' × ' + prices.yearly + ' MAD/an';
                        console.log('Yearly calculation:', yearsCount, 'x', prices.yearly, '=', total);
                    } else {
                        // Fallback to monthly, weekly, daily, or hourly
                        if (prices.monthly > 0) {
                            const monthsCount = yearsCount * 12;
                            total = monthsCount * prices.monthly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + monthsCount + ' mois)';
                            billingUnit = 'Facturation: ' + monthsCount + ' × ' + prices.monthly + ' MAD/mois (tarif mensuel)';
                        } else if (prices.weekly > 0) {
                            const weeksCount = yearsCount * 52;
                            total = weeksCount * prices.weekly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + weeksCount + ' semaines)';
                            billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                        } else if (prices.daily > 0) {
                            const daysCount = yearsCount * 365;
                            total = daysCount * prices.daily;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = yearsCount * 365 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Yearly fallback calculation:', total);
                    }
                    break;
                default:
                    const defaultHoursCount = parseFloat(document.getElementById('hours_count').value) || 1;
                    total = defaultHoursCount * prices.hourly;
                    durationText = defaultHoursCount + ' heure' + (defaultHoursCount > 1 ? 's' : '');
                    billingUnit = 'Facturation: ' + defaultHoursCount + ' × ' + prices.hourly + ' MAD/h';
                    console.log('Default hourly calculation:', defaultHoursCount, 'x', prices.hourly, '=', total);
            }
        } else {
            // Fixed pricing
            total = prices.fixed;
            durationText = '1 réservation';
            billingUnit = 'Facturation: Prix fixe';
            console.log('Fixed pricing:', total);
        }

        durationSpan.textContent = durationText;
        totalSpan.textContent = total.toFixed(2) + ' MAD';

        // Update billing unit display
        const billingUnitSpan = document.getElementById('billing-unit');
        if (billingUnitSpan) {
            billingUnitSpan.textContent = billingUnit;
        }

        // Update hidden fields for form submission
        const durationCountInput = document.getElementById('duration_count');
        const durationUnitInput = document.getElementById('duration_unit');

        if (durationCountInput && durationUnitInput) {
            let count = 1;
            let unit = 'hours';

            switch(selectedOption) {
                case 'hourly':
                    count = parseFloat(document.getElementById('hours_count').value) || 1;
                    unit = 'hours';
                    break;
                case 'daily':
                    count = parseInt(document.getElementById('days_count').value) || 1;
                    unit = 'days';
                    break;
                case 'weekly':
                    count = parseInt(document.getElementById('weeks_count').value) || 1;
                    unit = 'weeks';
                    break;
                case 'monthly':
                    count = parseInt(document.getElementById('months_count').value) || 1;
                    unit = 'months';
                    break;
                case 'yearly':
                    count = parseInt(document.getElementById('years_count').value) || 1;
                    unit = 'years';
                    break;
                default:
                    count = 1;
                    unit = 'fixed';
            }

            durationCountInput.value = count;
            durationUnitInput.value = unit;

            console.log('Updated hidden fields:', count, unit);
        }

        updateCurrentRateDisplay(selectedOption);
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Add event listeners for pricing option changes
    const pricingOptions = document.querySelectorAll('input[name="pricing_option"]');
    const selectedPricingTypeInput = document.getElementById('selected_pricing_type');

    pricingOptions.forEach(option => {
        option.addEventListener('change', function() {
            console.log('Pricing option changed to:', this.value); // Debug log
            updateDurationInputDisplay();
            calculateDurationAndCost();
            // Update visual selection
            updatePricingSelection();
            // Update hidden input
            if (selectedPricingTypeInput) {
                selectedPricingTypeInput.value = this.value;
            }
        });
    });

    // Function to show/hide duration input sections
    function updateDurationInputDisplay() {
        const selectedOption = getSelectedPricingOption();

        // Hide all duration input groups
        document.querySelectorAll('.duration-input-group').forEach(group => {
            group.style.display = 'none';
        });

        // Show the appropriate duration input group
        const targetGroup = document.getElementById(selectedOption + '-duration');
        if (targetGroup) {
            targetGroup.style.display = 'block';
        }

        console.log('Duration input display updated for:', selectedOption);
    }

    // Update pricing visual selection
    function updatePricingSelection() {
        const allLabels = document.querySelectorAll('.pricing-option-simple');
        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');

        allLabels.forEach(label => {
            label.classList.remove('selected');
        });

        if (selectedOption) {
            const selectedLabel = selectedOption.closest('.pricing-option-simple');
            if (selectedLabel) {
                selectedLabel.classList.add('selected');
            }
        }
    }

    // Add event listeners for duration inputs
    const durationInputs = [
        'hours_count', 'days_count', 'weeks_count', 'months_count', 'years_count'
    ];

    durationInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', calculateDurationAndCost);
            input.addEventListener('change', calculateDurationAndCost);
        }
    });

    // Initial setup
    updateDurationInputDisplay();
    calculateDurationAndCost();
    updatePricingSelection();

    // Enhanced availability check function
    function setupAvailabilityCheck() {
        const checkBtn = document.getElementById('checkAvailability');
        if (!checkBtn) return;

        checkBtn.addEventListener('click', function() {
            const date = document.getElementById('date').value;
            const durationType = document.querySelector('input[name="duration_type"]:checked')?.value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;
            const allDayValue = document.getElementById('all_day').value;
            const resultDiv = document.getElementById('availabilityResult');
            const localId = {{ $local->id }};

            // Clear previous results
            resultDiv.innerHTML = '';

            // Validate inputs
            if (!date) {
                resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez sélectionner une date.</div>';
                return;
            }

            // Handle different duration types
            let requestData = {
                date: date
            };

            if (durationType === 'all_day' || allDayValue === '1') {
                requestData.all_day = true;
                requestData.start_time = '00:00';
                requestData.end_time = '23:59';
            } else {
                if (!startTime || !endTime) {
                    resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez sélectionner les heures de début et fin.</div>';
                    return;
                }

                if (endTime <= startTime) {
                    resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>L\'heure de fin doit être après l\'heure de début.</div>';
                    return;
                }

                requestData.start_time = startTime;
                requestData.end_time = endTime;
                requestData.all_day = false;
            }

            // Show loading state
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification de la disponibilité...</div>';

            // Make API call
            fetch(`/api/locals/${localId}/check-availability`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Availability check response:', data);

                if (data.available) {
                    const timeInfo = requestData.all_day ? 'toute la journée' : `de ${startTime} à ${endTime}`;
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                                <div>
                                    <h6 class="alert-heading mb-1">Créneau disponible !</h6>
                                    <p class="mb-0">Le local est disponible ${timeInfo} pour la date sélectionnée.</p>
                                    ${data.message ? `<small class="text-muted">${data.message}</small>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-times-circle fa-2x text-danger me-3"></i>
                                <div>
                                    <h6 class="alert-heading mb-1">Créneau non disponible</h6>
                                    <p class="mb-0">${data.message || 'Ce créneau n\'est pas disponible.'}</p>
                                    <small class="text-muted">Veuillez choisir une autre date ou un autre horaire.</small>
                                </div>
                            </div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error checking availability:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">Erreur de vérification</h6>
                                <p class="mb-0">Une erreur s'est produite lors de la vérification de disponibilité.</p>
                                <small class="text-muted">Veuillez réessayer dans quelques instants.</small>
                            </div>
                        </div>
                        <hr>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="document.getElementById('checkAvailability').click()">
                            <i class="fas fa-redo me-1"></i>Réessayer
                        </button>
                    </div>
                `;
            });
        });
    }

    // Auto-check availability when date changes
    function setupAutoAvailabilityCheck() {
        const dateInput = document.getElementById('date');
        const availabilityInfo = document.getElementById('dateAvailabilityInfo');

        if (!dateInput || !availabilityInfo) return;

        dateInput.addEventListener('change', function() {
            const selectedDate = this.value;
            const localId = {{ $local->id }};

            if (!selectedDate) {
                availabilityInfo.innerHTML = '<i class="fas fa-info-circle me-1"></i>Sélectionnez une date pour voir la disponibilité';
                return;
            }

            // Show loading
            availabilityInfo.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Vérification de la disponibilité...';

            // Quick check for the date (general availability)
            fetch(`/api/locals/${localId}/calendar?year=${new Date(selectedDate).getFullYear()}&month=${new Date(selectedDate).getMonth() + 1}`)
                .then(response => response.json())
                .then(data => {
                    const dateKey = selectedDate;
                    const reservations = data.reservations[dateKey] || [];

                    if (reservations.length === 0) {
                        availabilityInfo.innerHTML = `
                            <div class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                <strong>Entièrement disponible</strong>
                                <br><small>Aucune réservation pour cette date</small>
                            </div>
                        `;
                    } else {
                        const confirmedReservations = reservations.filter(r => r.status === 'confirmée').length;
                        const pendingReservations = reservations.filter(r => r.status === 'en attente').length;

                        availabilityInfo.innerHTML = `
                            <div class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>Partiellement réservé</strong>
                                <br><small>${confirmedReservations} confirmée(s), ${pendingReservations} en attente</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error checking date availability:', error);
                    availabilityInfo.innerHTML = `
                        <div class="text-muted">
                            <i class="fas fa-question-circle me-1"></i>
                            Impossible de vérifier la disponibilité
                        </div>
                    `;
                });
        });
    }

    // New duration type handling
    function handleDurationTypeChange() {
        const durationTypes = document.querySelectorAll('input[name="duration_type"]');
        const specificSection = document.getElementById('specificTimeSection');
        const presetSection = document.getElementById('presetTimeSection');
        const allDayInput = document.getElementById('all_day');
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');

        durationTypes.forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove active class from all cards
                document.querySelectorAll('.duration-option').forEach(card => {
                    card.classList.remove('active');
                });

                // Add active class to selected card
                const selectedCard = document.querySelector(`.duration-option[data-type="${this.value}"]`);
                if (selectedCard) {
                    selectedCard.classList.add('active');
                }

                // Handle different duration types
                switch(this.value) {
                    case 'specific':
                        specificSection.style.display = 'block';
                        presetSection.style.display = 'none';
                        allDayInput.value = '0';
                        startTimeInput.required = true;
                        endTimeInput.required = true;
                        break;
                    case 'all_day':
                        specificSection.style.display = 'none';
                        presetSection.style.display = 'none';
                        allDayInput.value = '1';
                        startTimeInput.value = '00:00';
                        endTimeInput.value = '23:59';
                        startTimeInput.required = false;
                        endTimeInput.required = false;
                        break;
                    case 'preset':
                        specificSection.style.display = 'none';
                        presetSection.style.display = 'block';
                        allDayInput.value = '0';
                        startTimeInput.required = true;
                        endTimeInput.required = true;
                        break;
                }

                updateSummary();
            });
        });

        // Handle preset button clicks
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const startTime = this.dataset.start;
                const endTime = this.dataset.end;

                startTimeInput.value = startTime;
                endTimeInput.value = endTime;

                // Remove active class from all preset buttons
                document.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                updateSummary();
            });
        });
    }

    // Handle pricing card selection
    function handlePricingSelection() {
        const pricingCards = document.querySelectorAll('.pricing-card');
        const pricingRadios = document.querySelectorAll('input[name="pricing_option"]');

        pricingCards.forEach(card => {
            card.addEventListener('click', function() {
                const pricingType = this.dataset.pricing;
                const radio = document.getElementById(`${pricingType}_pricing`);

                if (radio) {
                    radio.checked = true;

                    // Remove active class from all cards
                    pricingCards.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked card
                    this.classList.add('active');

                    updateSummary();
                }
            });
        });

        pricingRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove active class from all cards
                pricingCards.forEach(c => c.classList.remove('active'));

                // Add active class to selected card
                const selectedCard = document.querySelector(`.pricing-card[data-pricing="${this.value}"]`);
                if (selectedCard) {
                    selectedCard.classList.add('active');
                }

                updateSummary();
            });
        });
    }

    // Update summary section
    function updateSummary() {
        const date = document.getElementById('date').value;
        const durationType = document.querySelector('input[name="duration_type"]:checked')?.value;
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;
        const pricingOption = document.querySelector('input[name="pricing_option"]:checked')?.value;

        // Update summary date
        if (date) {
            const dateObj = new Date(date);
            document.getElementById('summary-date').textContent = dateObj.toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Update summary time
        let timeText = '-';
        if (durationType === 'all_day') {
            timeText = 'Toute la journée (00:00 - 23:59)';
        } else if (startTime && endTime) {
            timeText = `${startTime} - ${endTime}`;
        }
        document.getElementById('summary-time').textContent = timeText;

        // Update summary duration
        let durationText = '-';
        if (durationType === 'all_day') {
            durationText = '24 heures';
        } else if (startTime && endTime) {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);
            if (diffHours > 0) {
                durationText = `${diffHours} heure${diffHours > 1 ? 's' : ''}`;
            }
        }
        document.getElementById('summary-duration').textContent = durationText;

        // Update summary pricing
        let pricingText = '-';
        if (pricingOption) {
            const local = @json($local);
            switch(pricingOption) {
                case 'hourly':
                    pricingText = `${local.hourly_price || local.price} MAD/heure`;
                    break;
                case 'daily':
                    pricingText = `${local.daily_price} MAD/jour`;
                    break;
                case 'weekly':
                    pricingText = `${local.weekly_price} MAD/semaine`;
                    break;
                case 'monthly':
                    pricingText = `${local.monthly_price} MAD/mois`;
                    break;
            }
        }
        document.getElementById('summary-pricing').textContent = pricingText;

        // Update total
        calculateTotal();
    }

    // Calculate total price
    function calculateTotal() {
        const durationType = document.querySelector('input[name="duration_type"]:checked')?.value;
        const pricingOption = document.querySelector('input[name="pricing_option"]:checked')?.value;
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;
        const local = @json($local);

        let total = 0;

        if (durationType === 'all_day') {
            if (local.daily_price && local.daily_price > 0) {
                total = local.daily_price;
            } else if (local.hourly_price) {
                total = local.hourly_price * 24;
            }
        } else if (startTime && endTime && pricingOption) {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);

            switch(pricingOption) {
                case 'hourly':
                    total = (local.hourly_price || local.price) * diffHours;
                    break;
                case 'daily':
                    total = local.daily_price;
                    break;
                case 'weekly':
                    total = local.weekly_price;
                    break;
                case 'monthly':
                    total = local.monthly_price;
                    break;
            }
        }

        document.getElementById('summary-total').textContent = `${total.toFixed(0)} MAD`;
    }

    // Enable/disable submit button based on terms checkbox
    function handleTermsCheckbox() {
        const termsCheckbox = document.getElementById('terms');
        const submitBtn = document.getElementById('submitBtn');

        termsCheckbox.addEventListener('change', function() {
            submitBtn.disabled = !this.checked;
        });
    }

    // Update duration display based on time selection
    function updateDurationDisplay() {
        const allDay = document.getElementById('all_day').checked;
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;

        if (allDay) {
            document.getElementById('duration').textContent = '24 heures (toute la journée)';
            // Update pricing calculations for all-day
            updatePricingForAllDay();
        } else if (startTime && endTime) {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);

            if (diffHours > 0) {
                document.getElementById('duration').textContent = `${diffHours} heure${diffHours > 1 ? 's' : ''}`;
                // Update pricing calculations
                updatePricingCalculations(diffHours);
            }
        }
    }

    // Update pricing for all-day reservations
    function updatePricingForAllDay() {
        const local = @json($local);

        // For all-day, use daily price if available, otherwise calculate 24 hours
        if (local.daily_price && local.daily_price > 0) {
            document.getElementById('current-rate').textContent = local.daily_price + ' MAD/jour';
            document.getElementById('billing-unit').textContent = '1 × ' + local.daily_price + ' MAD/jour';
            document.getElementById('total').textContent = local.daily_price + ' MAD';
        } else if (local.hourly_price) {
            const allDayPrice = local.hourly_price * 24;
            document.getElementById('current-rate').textContent = local.hourly_price + ' MAD/h';
            document.getElementById('billing-unit').textContent = '24 × ' + local.hourly_price + ' MAD/h';
            document.getElementById('total').textContent = allDayPrice + ' MAD';
        }
    }

    // Update pricing calculations for hourly reservations
    function updatePricingCalculations(hours) {
        const local = @json($local);

        if (local.pricing_type === 'flexible' && local.hourly_price) {
            const totalPrice = local.hourly_price * hours;
            document.getElementById('current-rate').textContent = local.hourly_price + ' MAD/h';
            document.getElementById('billing-unit').textContent = hours + ' × ' + local.hourly_price + ' MAD/h';
            document.getElementById('total').textContent = totalPrice + ' MAD';
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all handlers
        handleDurationTypeChange();
        handlePricingSelection();
        handleTermsCheckbox();
        setupAvailabilityCheck();
        setupAutoAvailabilityCheck();

        // Check URL parameters for pre-selection
        const urlParams = new URLSearchParams(window.location.search);

        // Handle all_day parameter
        if (urlParams.get('all_day') === '1') {
            document.getElementById('all_day_radio').checked = true;
            document.querySelector('.duration-option[data-type="all_day"]').classList.add('active');
            document.getElementById('specificTimeSection').style.display = 'none';
            document.getElementById('presetTimeSection').style.display = 'none';
            document.getElementById('all_day').value = '1';
            document.getElementById('start_time').value = '00:00';
            document.getElementById('end_time').value = '23:59';
        } else {
            // Default to specific time
            document.getElementById('specific_time').checked = true;
            document.querySelector('.duration-option[data-type="specific"]').classList.add('active');
        }

        // Handle pre-filled times from URL
        const urlStartTime = urlParams.get('start_time');
        const urlEndTime = urlParams.get('end_time');
        if (urlStartTime) document.getElementById('start_time').value = urlStartTime;
        if (urlEndTime) document.getElementById('end_time').value = urlEndTime;

        // Handle pre-filled date from URL
        const urlDate = urlParams.get('date');
        if (urlDate) document.getElementById('date').value = urlDate;

        // Set initial active pricing card
        const checkedPricing = document.querySelector('input[name="pricing_option"]:checked');
        if (checkedPricing) {
            const pricingCard = document.querySelector(`.pricing-card[data-pricing="${checkedPricing.value}"]`);
            if (pricingCard) {
                pricingCard.classList.add('active');
            }
        }

        // Add event listeners for real-time updates
        document.getElementById('date').addEventListener('change', updateSummary);
        document.getElementById('start_time').addEventListener('change', updateSummary);
        document.getElementById('end_time').addEventListener('change', updateSummary);

        // Initial summary update
        updateSummary();
    });

    // Add form submission debugging
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission started');

            // Check required fields
            const date = document.getElementById('date').value;
            const allDay = document.getElementById('all_day').checked;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;
            const terms = document.getElementById('terms').checked;

            console.log('Form data:', {
                date: date,
                all_day: allDay,
                start_time: startTime,
                end_time: endTime,
                terms: terms,
                selected_pricing_type: document.getElementById('selected_pricing_type').value,
                duration_count: document.getElementById('duration_count').value,
                duration_unit: document.getElementById('duration_unit').value
            });

            if (!date) {
                e.preventDefault();
                alert('Veuillez sélectionner une date.');
                return false;
            }

            if (!allDay && (!startTime || !endTime)) {
                e.preventDefault();
                alert('Veuillez remplir les heures de début et fin, ou sélectionner "toute la journée".');
                return false;
            }

            if (!terms) {
                e.preventDefault();
                alert('Veuillez accepter les conditions d\'utilisation.');
                return false;
            }

            // Check if end time is after start time (only for non-all-day)
            if (!allDay && endTime <= startTime) {
                e.preventDefault();
                alert('L\'heure de fin doit être après l\'heure de début.');
                return false;
            }

            console.log('Form validation passed, submitting...');
        });
    }
});
</script>
@endpush

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Simple Pricing Options Styling */
.pricing-option-simple {
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0;
}

.pricing-option-simple:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-option-simple.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.pricing-option-simple input[type="radio"] {
    display: none;
}

.pricing-simple {
    text-align: center;
}

.pricing-simple strong {
    color: #007bff;
    font-size: 1.1rem;
}

.pricing-simple small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .pricing-option-simple {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}
</style>
@endpush
@endsection
