@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="@if($local->pricing_type === 'flexible')hourly @else fixed @endif">
                        <input type="hidden" name="duration_count" id="duration_count" value="1">
                        <input type="hidden" name="duration_unit" id="duration_unit" value="@if($local->pricing_type === 'flexible')hours @else fixed @endif">

                        <!-- Date and Billing Type Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation *
                                </label>
                                <input type="date"
                                       class="form-control @error('date') is-invalid @enderror"
                                       id="date"
                                       name="date"
                                       value="{{ old('date', now()->addDay()->format('Y-m-d')) }}"
                                       min="{{ now()->format('Y-m-d') }}"
                                       required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="billing_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Type de facturation *
                                </label>
                                <select class="form-select @error('billing_type') is-invalid @enderror"
                                        id="billing_type"
                                        name="billing_type"
                                        required>
                                    <option value="">Choisir le type</option>
                                    @if($local->pricing_type === 'fixed')
                                        <option value="fixed" selected>Prix fixe - {{ $local->price }} MAD</option>
                                    @else
                                        @if($local->hourly_price && $local->hourly_price > 0)
                                        <option value="hourly" {{ old('billing_type') == 'hourly' ? 'selected' : '' }}>
                                            Horaire - {{ $local->hourly_price }} MAD/heure
                                        </option>
                                        @endif
                                        @if($local->daily_price && $local->daily_price > 0)
                                        <option value="daily" {{ old('billing_type') == 'daily' ? 'selected' : '' }}>
                                            Journalier - {{ $local->daily_price }} MAD/jour
                                        </option>
                                        @endif
                                        @if($local->weekly_price && $local->weekly_price > 0)
                                        <option value="weekly" {{ old('billing_type') == 'weekly' ? 'selected' : '' }}>
                                            Hebdomadaire - {{ $local->weekly_price }} MAD/semaine
                                        </option>
                                        @endif
                                        @if($local->monthly_price && $local->monthly_price > 0)
                                        <option value="monthly" {{ old('billing_type') == 'monthly' ? 'selected' : '' }}>
                                            Mensuel - {{ $local->monthly_price }} MAD/mois
                                        </option>
                                        @endif
                                    @endif
                                </select>
                                @error('billing_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Hidden quantity field - automatically set based on billing type -->
                        <input type="hidden" id="quantity" name="quantity" value="1">

                        <!-- Price Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>Prix total:
                                            <span id="total_price_display" class="text-success">
                                                @if($local->pricing_type === 'fixed')
                                                    {{ $local->price }} MAD
                                                @else
                                                    {{ $local->hourly_price ?? 0 }} MAD
                                                @endif
                                            </span>
                                        </h5>
                                        <small class="text-muted" id="billing_info">Sélectionnez un type de facturation</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hours Selection (only for hourly billing) -->
                        <div class="row mb-4" id="hours_selection" style="display: none;">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-clock me-2"></i>Sélection des heures
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label for="start_time" class="form-label">
                                                    <i class="fas fa-play me-1"></i>Heure de début *
                                                </label>
                                                <input type="time"
                                                       class="form-control @error('start_time') is-invalid @enderror"
                                                       id="start_time"
                                                       name="start_time"
                                                       value="{{ old('start_time', '09:00') }}">
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-md-6">
                                                <label for="end_time" class="form-label">
                                                    <i class="fas fa-stop me-1"></i>Heure de fin *
                                                </label>
                                                <input type="time"
                                                       class="form-control @error('end_time') is-invalid @enderror"
                                                       id="end_time"
                                                       name="end_time"
                                                       value="{{ old('end_time', '10:00') }}">
                                                @error('end_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- All Day Option (for daily billing) -->
                        <div class="row mb-4" id="all_day_option" style="display: none;">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_day" name="all_day" value="1"
                                           {{ old('all_day') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="all_day">
                                        <i class="fas fa-sun me-1"></i>Réserver toute la journée (00:00 - 23:59)
                                    </label>
                                </div>
                            </div>
                        </div>





                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h5">
                                                    @if($local->pricing_type === 'flexible')
                                                        1 heure
                                                    @else
                                                        1 réservation
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-tag me-2"></i>Tarif</h5>
                                                <div id="selected-pricing">
                                                    @if($local->pricing_type === 'flexible')
                                                        <span class="h6" id="current-rate">{{ $local->hourly_price }} MAD/h</span>
                                                    @else
                                                        <span class="h6">{{ $local->price }} MAD/réservation</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-calculator me-2"></i>Facturation</h5>
                                                <span id="billing-unit" class="h6">
                                                    @if($local->pricing_type === 'flexible')
                                                        1 × {{ $local->hourly_price }} MAD/h
                                                    @else
                                                        Prix fixe
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-euro-sign me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    @if($local->pricing_type === 'flexible')
                                                        {{ $local->hourly_price ?? $local->price }} MAD
                                                    @else
                                                        {{ $local->price }} MAD
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '{{ $local->pricing_type }}';
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }},
        yearly: {{ $local->yearly_price ?? 0 }}
    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        let rateText = '';

        switch(option) {
            case 'hourly':
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
                break;
            case 'daily':
                if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'weekly':
                if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'monthly':
                if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'yearly':
                if (prices.yearly > 0) {
                    rateText = `${prices.yearly} MAD/an`;
                } else if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois (tarif mensuel)`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'fixed':
                rateText = `${prices.fixed} MAD/réservation`;
                break;
            default:
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
        }

        currentRateSpan.textContent = rateText;
    }

    // Add event listeners
    const billingTypeSelect = document.getElementById('billing_type');
    const quantityInput = document.getElementById('quantity');
    const hoursSelection = document.getElementById('hours_selection');
    const allDayOption = document.getElementById('all_day_option');
    const totalPriceDisplay = document.getElementById('total_price_display');
    const billingInfo = document.getElementById('billing_info');

    // Handle billing type change
    function handleBillingTypeChange() {
        const selectedType = billingTypeSelect.value;
        const hoursSelection = document.getElementById('hours_selection');
        const timePresets = document.getElementById('time_presets');
        const allDayOption = document.getElementById('all_day_option');
        const billingInfo = document.getElementById('billing_info');

        // Hide all conditional sections
        if (hoursSelection) hoursSelection.style.display = 'none';
        if (timePresets) timePresets.style.display = 'none';
        if (allDayOption) allDayOption.style.display = 'none';

        // Set quantity to 1 for all billing types (no manual quantity input)
        quantityInput.value = 1;

        // Update display based on billing type
        switch(selectedType) {
            case 'hourly':
                if (hoursSelection) hoursSelection.style.display = 'block';
                if (timePresets) timePresets.style.display = 'block';
                if (billingInfo) billingInfo.textContent = 'Sélectionnez les heures de début et fin';
                break;
            case 'daily':
                if (allDayOption) allDayOption.style.display = 'block';
                if (billingInfo) billingInfo.textContent = 'Réservation pour 1 jour complet';
                break;
            case 'weekly':
                if (billingInfo) billingInfo.textContent = 'Réservation pour 1 semaine complète';
                break;
            case 'monthly':
                if (billingInfo) billingInfo.textContent = 'Réservation pour 1 mois complet';
                break;
            case 'fixed':
                if (billingInfo) billingInfo.textContent = 'Prix fixe pour la réservation';
                break;
            default:
                if (billingInfo) billingInfo.textContent = 'Sélectionnez un type de facturation';
        }

        // Update hidden fields
        document.getElementById('selected_pricing_type').value = selectedType;
        document.getElementById('duration_unit').value = selectedType;

        calculatePrice();
    }

    // Calculate price based on selection
    function calculatePrice() {
        const selectedType = billingTypeSelect.value;
        const quantity = parseInt(quantityInput.value) || 1;
        let price = 0;

        if (!selectedType) {
            totalPriceDisplay.textContent = '0 MAD';
            return;
        }

        switch(selectedType) {
            case 'hourly':
                price = (local.hourly_price || 0) * quantity;
                break;
            case 'daily':
                price = (local.daily_price || 0) * quantity;
                break;
            case 'weekly':
                price = (local.weekly_price || 0) * quantity;
                break;
            case 'monthly':
                price = (local.monthly_price || 0) * quantity;
                break;
            case 'fixed':
                price = (local.price || 0) * quantity;
                break;
        }

        totalPriceDisplay.textContent = Math.round(price) + ' MAD';
        document.getElementById('duration_count').value = quantity;
    }

    // Quick time slot function
    function setTimeSlot(startTime, endTime) {
        document.getElementById('start_time').value = startTime;
        document.getElementById('end_time').value = endTime;

        // Calculate hours automatically for hourly billing
        if (billingTypeSelect.value === 'hourly') {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);

            if (diffHours > 0) {
                quantityInput.value = diffHours;
                calculatePrice();
            }
        }
    }

    // Make function global for onclick handlers
    window.setTimeSlot = setTimeSlot;

    // Add event listeners
    if (billingTypeSelect) {
        billingTypeSelect.addEventListener('change', handleBillingTypeChange);
    }
    if (quantityInput) {
        quantityInput.addEventListener('input', calculatePrice);
    }

    // Initialize form
    if (billingTypeSelect.value) {
        handleBillingTypeChange();
    } else if (local.pricing_type === 'fixed') {
        // Auto-select fixed pricing if it's the only option
        billingTypeSelect.value = 'fixed';
        handleBillingTypeChange();
    }
});

// Simple availability check function
const checkBtn = document.getElementById('checkAvailability');
if (checkBtn) {
    checkBtn.addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const billingType = document.getElementById('billing_type').value;
        const resultDiv = document.getElementById('availabilityResult');
        const localId = local.id;

        // Clear previous results
        resultDiv.innerHTML = '';

        // Validate inputs
        if (!date) {
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez sélectionner une date.</div>';
            return;
        }

        if (!billingType) {
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez choisir le type de facturation.</div>';
            return;
        }

        // Show loading state
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification de la disponibilité...</div>';

        // Prepare request data based on billing type
        let requestData = { date: date };

        if (billingType === 'hourly') {
            const startTime = document.getElementById('start_time').value || '09:00';
            const endTime = document.getElementById('end_time').value || '10:00';

            if (endTime <= startTime) {
                resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>L\'heure de fin doit être après l\'heure de début.</div>';
                return;
            }

            requestData.start_time = startTime;
            requestData.end_time = endTime;
            requestData.all_day = false;
        } else {
            // For other billing types, check general availability
            const allDay = document.getElementById('all_day');
            if (allDay && allDay.checked) {
                requestData.start_time = '00:00';
                requestData.end_time = '23:59';
                requestData.all_day = true;
            } else {
                requestData.start_time = '00:00';
                requestData.end_time = '23:59';
                requestData.all_day = true;
            }
        }

        console.log('Checking availability with data:', requestData);

        // Make API call
        fetch(`/api/locals/${localId}/check-availability`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Availability response:', data);

            if (data.available) {
                let timeInfo = '';
                if (billingType === 'hourly') {
                    timeInfo = `de ${requestData.start_time} à ${requestData.end_time}`;
                } else {
                    timeInfo = 'pour la période sélectionnée';
                }

                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">✅ Disponible !</h6>
                                <p class="mb-0">Le local est disponible ${timeInfo}.</p>
                                ${data.message ? `<small class="text-muted">${data.message}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-times-circle fa-2x text-danger me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">❌ Non disponible</h6>
                                <p class="mb-0">${data.message || 'Ce créneau n\'est pas disponible.'}</p>
                                <small class="text-muted">Veuillez choisir une autre date ou un autre horaire.</small>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error checking availability:', error);
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">⚠️ Erreur</h6>
                            <p class="mb-0">Erreur lors de la vérification de disponibilité.</p>
                            <small class="text-muted">Veuillez réessayer.</small>
                        </div>
                    </div>
                </div>
            `;
        });
    });
}
</script>
@endpush

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Simple Pricing Options Styling */
.pricing-option-simple {
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0;
}

.pricing-option-simple:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-option-simple.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.pricing-option-simple input[type="radio"] {
    display: none;
}

.pricing-simple {
    text-align: center;
}

.pricing-simple strong {
    color: #007bff;
    font-size: 1.1rem;
}

.pricing-simple small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .pricing-option-simple {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}
</style>
@endpush
@endsection