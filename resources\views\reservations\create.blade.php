@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Display validation errors -->
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="@if($local->pricing_type === 'flexible')hourly @else fixed @endif">
                        <input type="hidden" name="duration_count" id="duration_count" value="1">
                        <input type="hidden" name="duration_unit" id="duration_unit" value="@if($local->pricing_type === 'flexible')hours @else fixed @endif">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation
                                </label>
                                <input type="date"
                                       class="form-control @error('date') is-invalid @enderror"
                                       id="date"
                                       name="date"
                                       value="{{ old('date', now()->addDay()->format('Y-m-d')) }}"
                                       min="{{ now()->format('Y-m-d') }}"
                                       required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de début
                                </label>
                                <input type="time"
                                       class="form-control @error('start_time') is-invalid @enderror"
                                       id="start_time"
                                       name="start_time"
                                       value="{{ old('start_time', '09:00') }}"
                                       required>
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de fin
                                </label>
                                <input type="time"
                                       class="form-control @error('end_time') is-invalid @enderror"
                                       id="end_time"
                                       name="end_time"
                                       value="{{ old('end_time', '10:00') }}"
                                       required>
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Simple Pricing Selection -->
                        @if($local->pricing_type === 'flexible')
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-gradient-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Choisissez votre tarif</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @if($local->hourly_price)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="hourly" checked>
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->hourly_price }} MAD/heure</strong>
                                                        <small class="d-block text-muted">Idéal pour réunions courtes</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->daily_price && $local->daily_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="daily">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->daily_price }} MAD/jour</strong>
                                                        <small class="d-block text-muted">Parfait pour ateliers</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->weekly_price && $local->weekly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="weekly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->weekly_price }} MAD/semaine</strong>
                                                        <small class="d-block text-muted">Idéal pour projets</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->monthly_price && $local->monthly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="monthly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->monthly_price }} MAD/mois</strong>
                                                        <small class="d-block text-muted">Pour locations longues</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->yearly_price && $local->yearly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="yearly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->yearly_price }} MAD/an</strong>
                                                        <small class="d-block text-muted">Contrats annuels</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Duration Input Based on Selected Pricing -->
                        <div class="row mb-4" id="duration-input-section">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-gradient-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Durée de location</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Hourly Duration Input -->
                                        <div id="hourly-duration" class="duration-input-group">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="hours_count" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Nombre d'heures
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="hours_count"
                                                               name="hours_count"
                                                               min="1"
                                                               max="24"
                                                               value="1"
                                                               step="0.5">
                                                        <span class="input-group-text">heure(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 24 heures</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif horaire:</strong> {{ $local->hourly_price }} MAD/heure
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Daily Duration Input -->
                                        <div id="daily-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="days_count" class="form-label">
                                                        <i class="fas fa-calendar-day me-1"></i>Nombre de jours
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="days_count"
                                                               name="days_count"
                                                               min="1"
                                                               max="30"
                                                               value="1">
                                                        <span class="input-group-text">jour(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 30 jours</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif journalier:</strong> {{ $local->daily_price }} MAD/jour
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Weekly Duration Input -->
                                        <div id="weekly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="weeks_count" class="form-label">
                                                        <i class="fas fa-calendar-week me-1"></i>Nombre de semaines
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="weeks_count"
                                                               name="weeks_count"
                                                               min="1"
                                                               max="12"
                                                               value="1">
                                                        <span class="input-group-text">semaine(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 12 semaines</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif hebdomadaire:</strong> {{ $local->weekly_price }} MAD/semaine
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Monthly Duration Input -->
                                        <div id="monthly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="months_count" class="form-label">
                                                        <i class="fas fa-calendar-alt me-1"></i>Nombre de mois
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="months_count"
                                                               name="months_count"
                                                               min="1"
                                                               max="12"
                                                               value="1">
                                                        <span class="input-group-text">mois</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 12 mois</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif mensuel:</strong> {{ $local->monthly_price }} MAD/mois
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Yearly Duration Input -->
                                        <div id="yearly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="years_count" class="form-label">
                                                        <i class="fas fa-calendar me-1"></i>Nombre d'années
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="years_count"
                                                               name="years_count"
                                                               min="1"
                                                               max="5"
                                                               value="1">
                                                        <span class="input-group-text">année(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 5 années</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif annuel:</strong> {{ $local->yearly_price }} MAD/an
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h5">
                                                    @if($local->pricing_type === 'flexible')
                                                        1 heure
                                                    @else
                                                        1 réservation
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-tag me-2"></i>Tarif</h5>
                                                <div id="selected-pricing">
                                                    @if($local->pricing_type === 'flexible')
                                                        <span class="h6" id="current-rate">{{ $local->hourly_price }} MAD/h</span>
                                                    @else
                                                        <span class="h6">{{ $local->price }} MAD/réservation</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-calculator me-2"></i>Facturation</h5>
                                                <span id="billing-unit" class="h6">
                                                    @if($local->pricing_type === 'flexible')
                                                        1 × {{ $local->hourly_price }} MAD/h
                                                    @else
                                                        Prix fixe
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-euro-sign me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    @if($local->pricing_type === 'flexible')
                                                        {{ $local->hourly_price ?? $local->price }} MAD
                                                    @else
                                                        {{ $local->price }} MAD
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '{{ $local->pricing_type }}';
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }},
        yearly: {{ $local->yearly_price ?? 0 }}
    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        let rateText = '';

        switch(option) {
            case 'hourly':
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
                break;
            case 'daily':
                if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'weekly':
                if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'monthly':
                if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'yearly':
                if (prices.yearly > 0) {
                    rateText = `${prices.yearly} MAD/an`;
                } else if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois (tarif mensuel)`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'fixed':
                rateText = `${prices.fixed} MAD/réservation`;
                break;
            default:
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
        }

        currentRateSpan.textContent = rateText;
    }

    function calculateDurationAndCost() {
        const selectedOption = getSelectedPricingOption();
        console.log('Selected pricing option:', selectedOption); // Debug log
        console.log('Available prices:', prices); // Debug log

        let total = 0;
        let durationText = '';
        let billingUnit = '';

        if (pricingType === 'flexible') {
            // Calculate based on selected pricing option and user input
            switch(selectedOption) {
                case 'hourly':
                    const hoursCount = parseFloat(document.getElementById('hours_count').value) || 1;
                    if (prices.hourly > 0) {
                        total = hoursCount * prices.hourly;
                        durationText = hoursCount + ' heure' + (hoursCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h';
                        console.log('Hourly calculation:', hoursCount, 'x', prices.hourly, '=', total);
                    }
                    break;
                case 'daily':
                    const daysCount = parseInt(document.getElementById('days_count').value) || 1;
                    if (prices.daily > 0) {
                        total = daysCount * prices.daily;
                        durationText = daysCount + ' jour' + (daysCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour';
                        console.log('Daily calculation:', daysCount, 'x', prices.daily, '=', total);
                    } else {
                        // Fallback to hourly if daily not available
                        const hoursCount = daysCount * 24;
                        total = hoursCount * prices.hourly;
                        durationText = daysCount + ' jour' + (daysCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                        billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        console.log('Daily fallback to hourly:', hoursCount, 'x', prices.hourly, '=', total);
                    }
                    break;
                case 'weekly':
                    const weeksCount = parseInt(document.getElementById('weeks_count').value) || 1;
                    if (prices.weekly > 0) {
                        total = weeksCount * prices.weekly;
                        durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine';
                        console.log('Weekly calculation:', weeksCount, 'x', prices.weekly, '=', total);
                    } else {
                        // Fallback to daily or hourly
                        if (prices.daily > 0) {
                            const daysCount = weeksCount * 7;
                            total = daysCount * prices.daily;
                            durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '') + ' (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = weeksCount * 7 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = weeksCount + ' semaine' + (weeksCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Weekly fallback calculation:', total);
                    }
                    break;
                case 'monthly':
                    const monthsCount = parseInt(document.getElementById('months_count').value) || 1;
                    if (prices.monthly > 0) {
                        total = monthsCount * prices.monthly;
                        durationText = monthsCount + ' mois';
                        billingUnit = 'Facturation: ' + monthsCount + ' × ' + prices.monthly + ' MAD/mois';
                        console.log('Monthly calculation:', monthsCount, 'x', prices.monthly, '=', total);
                    } else {
                        // Fallback to weekly, daily, or hourly
                        if (prices.weekly > 0) {
                            const weeksCount = monthsCount * 4;
                            total = weeksCount * prices.weekly;
                            durationText = monthsCount + ' mois (' + weeksCount + ' semaines)';
                            billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                        } else if (prices.daily > 0) {
                            const daysCount = monthsCount * 30;
                            total = daysCount * prices.daily;
                            durationText = monthsCount + ' mois (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = monthsCount * 30 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = monthsCount + ' mois (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Monthly fallback calculation:', total);
                    }
                    break;
                case 'yearly':
                    const yearsCount = parseInt(document.getElementById('years_count').value) || 1;
                    if (prices.yearly > 0) {
                        total = yearsCount * prices.yearly;
                        durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '');
                        billingUnit = 'Facturation: ' + yearsCount + ' × ' + prices.yearly + ' MAD/an';
                        console.log('Yearly calculation:', yearsCount, 'x', prices.yearly, '=', total);
                    } else {
                        // Fallback to monthly, weekly, daily, or hourly
                        if (prices.monthly > 0) {
                            const monthsCount = yearsCount * 12;
                            total = monthsCount * prices.monthly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + monthsCount + ' mois)';
                            billingUnit = 'Facturation: ' + monthsCount + ' × ' + prices.monthly + ' MAD/mois (tarif mensuel)';
                        } else if (prices.weekly > 0) {
                            const weeksCount = yearsCount * 52;
                            total = weeksCount * prices.weekly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + weeksCount + ' semaines)';
                            billingUnit = 'Facturation: ' + weeksCount + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                        } else if (prices.daily > 0) {
                            const daysCount = yearsCount * 365;
                            total = daysCount * prices.daily;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + daysCount + ' jours)';
                            billingUnit = 'Facturation: ' + daysCount + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                        } else {
                            const hoursCount = yearsCount * 365 * 24;
                            total = hoursCount * prices.hourly;
                            durationText = yearsCount + ' année' + (yearsCount > 1 ? 's' : '') + ' (' + hoursCount + ' heures)';
                            billingUnit = 'Facturation: ' + hoursCount + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                        }
                        console.log('Yearly fallback calculation:', total);
                    }
                    break;
                default:
                    const defaultHoursCount = parseFloat(document.getElementById('hours_count').value) || 1;
                    total = defaultHoursCount * prices.hourly;
                    durationText = defaultHoursCount + ' heure' + (defaultHoursCount > 1 ? 's' : '');
                    billingUnit = 'Facturation: ' + defaultHoursCount + ' × ' + prices.hourly + ' MAD/h';
                    console.log('Default hourly calculation:', defaultHoursCount, 'x', prices.hourly, '=', total);
            }
        } else {
            // Fixed pricing
            total = prices.fixed;
            durationText = '1 réservation';
            billingUnit = 'Facturation: Prix fixe';
            console.log('Fixed pricing:', total);
        }

        durationSpan.textContent = durationText;
        totalSpan.textContent = total.toFixed(2) + ' MAD';

        // Update billing unit display
        const billingUnitSpan = document.getElementById('billing-unit');
        if (billingUnitSpan) {
            billingUnitSpan.textContent = billingUnit;
        }

        // Update hidden fields for form submission
        const durationCountInput = document.getElementById('duration_count');
        const durationUnitInput = document.getElementById('duration_unit');

        if (durationCountInput && durationUnitInput) {
            let count = 1;
            let unit = 'hours';

            switch(selectedOption) {
                case 'hourly':
                    count = parseFloat(document.getElementById('hours_count').value) || 1;
                    unit = 'hours';
                    break;
                case 'daily':
                    count = parseInt(document.getElementById('days_count').value) || 1;
                    unit = 'days';
                    break;
                case 'weekly':
                    count = parseInt(document.getElementById('weeks_count').value) || 1;
                    unit = 'weeks';
                    break;
                case 'monthly':
                    count = parseInt(document.getElementById('months_count').value) || 1;
                    unit = 'months';
                    break;
                case 'yearly':
                    count = parseInt(document.getElementById('years_count').value) || 1;
                    unit = 'years';
                    break;
                default:
                    count = 1;
                    unit = 'fixed';
            }

            durationCountInput.value = count;
            durationUnitInput.value = unit;

            console.log('Updated hidden fields:', count, unit);
        }

        updateCurrentRateDisplay(selectedOption);
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Add event listeners for pricing option changes
    const pricingOptions = document.querySelectorAll('input[name="pricing_option"]');
    const selectedPricingTypeInput = document.getElementById('selected_pricing_type');

    pricingOptions.forEach(option => {
        option.addEventListener('change', function() {
            console.log('Pricing option changed to:', this.value); // Debug log
            updateDurationInputDisplay();
            calculateDurationAndCost();
            // Update visual selection
            updatePricingSelection();
            // Update hidden input
            if (selectedPricingTypeInput) {
                selectedPricingTypeInput.value = this.value;
            }
        });
    });

    // Function to show/hide duration input sections
    function updateDurationInputDisplay() {
        const selectedOption = getSelectedPricingOption();

        // Hide all duration input groups
        document.querySelectorAll('.duration-input-group').forEach(group => {
            group.style.display = 'none';
        });

        // Show the appropriate duration input group
        const targetGroup = document.getElementById(selectedOption + '-duration');
        if (targetGroup) {
            targetGroup.style.display = 'block';
        }

        console.log('Duration input display updated for:', selectedOption);
    }

    // Update pricing visual selection
    function updatePricingSelection() {
        const allLabels = document.querySelectorAll('.pricing-option-simple');
        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');

        allLabels.forEach(label => {
            label.classList.remove('selected');
        });

        if (selectedOption) {
            const selectedLabel = selectedOption.closest('.pricing-option-simple');
            if (selectedLabel) {
                selectedLabel.classList.add('selected');
            }
        }
    }

    // Add event listeners for duration inputs
    const durationInputs = [
        'hours_count', 'days_count', 'weeks_count', 'months_count', 'years_count'
    ];

    durationInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', calculateDurationAndCost);
            input.addEventListener('change', calculateDurationAndCost);
        }
    });

    // Initial setup
    updateDurationInputDisplay();
    calculateDurationAndCost();
    updatePricingSelection();

    // Check availability
    document.getElementById('checkAvailability').addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const resultDiv = document.getElementById('availabilityResult');

        if (!date || !startTime || !endTime) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez remplir tous les champs de date et heure.</div>';
            return;
        }

        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification en cours...</div>';

        // Simulate API call
        setTimeout(function() {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Créneau disponible !</div>';
        }, 1000);
    });

    // Add form submission debugging
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission started');

            // Check required fields
            const date = document.getElementById('date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;
            const terms = document.getElementById('terms').checked;

            console.log('Form data:', {
                date: date,
                start_time: startTime,
                end_time: endTime,
                terms: terms,
                selected_pricing_type: document.getElementById('selected_pricing_type').value,
                duration_count: document.getElementById('duration_count').value,
                duration_unit: document.getElementById('duration_unit').value
            });

            if (!date || !startTime || !endTime) {
                e.preventDefault();
                alert('Veuillez remplir tous les champs obligatoires (date, heure de début, heure de fin).');
                return false;
            }

            if (!terms) {
                e.preventDefault();
                alert('Veuillez accepter les conditions d\'utilisation.');
                return false;
            }

            // Check if end time is after start time
            if (endTime <= startTime) {
                e.preventDefault();
                alert('L\'heure de fin doit être après l\'heure de début.');
                return false;
            }

            console.log('Form validation passed, submitting...');
        });
    }
});
</script>
@endpush

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Simple Pricing Options Styling */
.pricing-option-simple {
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0;
}

.pricing-option-simple:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-option-simple.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.pricing-option-simple input[type="radio"] {
    display: none;
}

.pricing-simple {
    text-align: center;
}

.pricing-simple strong {
    color: #007bff;
    font-size: 1.1rem;
}

.pricing-simple small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .pricing-option-simple {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}
</style>
@endpush
@endsection
