@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="@if($local->pricing_type === 'flexible')hourly @else fixed @endif">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation
                                </label>
                                <input type="date"
                                       class="form-control @error('date') is-invalid @enderror"
                                       id="date"
                                       name="date"
                                       value="{{ old('date', now()->addDay()->format('Y-m-d')) }}"
                                       min="{{ now()->format('Y-m-d') }}"
                                       required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de début
                                </label>
                                <input type="time"
                                       class="form-control @error('start_time') is-invalid @enderror"
                                       id="start_time"
                                       name="start_time"
                                       value="{{ old('start_time', '09:00') }}"
                                       required>
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de fin
                                </label>
                                <input type="time"
                                       class="form-control @error('end_time') is-invalid @enderror"
                                       id="end_time"
                                       name="end_time"
                                       value="{{ old('end_time', '10:00') }}"
                                       required>
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Simple Pricing Selection -->
                        @if($local->pricing_type === 'flexible')
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-gradient-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Choisissez votre tarif</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @if($local->hourly_price)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="hourly" checked>
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->hourly_price }} MAD/heure</strong>
                                                        <small class="d-block text-muted">Idéal pour réunions courtes</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->daily_price && $local->daily_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="daily">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->daily_price }} MAD/jour</strong>
                                                        <small class="d-block text-muted">Parfait pour ateliers</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->weekly_price && $local->weekly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="weekly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->weekly_price }} MAD/semaine</strong>
                                                        <small class="d-block text-muted">Idéal pour projets</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->monthly_price && $local->monthly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="monthly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->monthly_price }} MAD/mois</strong>
                                                        <small class="d-block text-muted">Pour locations longues</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif

                                            @if($local->yearly_price && $local->yearly_price > 0)
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="yearly">
                                                    <div class="pricing-simple">
                                                        <strong>{{ $local->yearly_price }} MAD/an</strong>
                                                        <small class="d-block text-muted">Contrats annuels</small>
                                                    </div>
                                                </label>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h5">1 heure</span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-tag me-2"></i>Tarif</h5>
                                                <div id="selected-pricing">
                                                    @if($local->pricing_type === 'flexible')
                                                        <span class="h6" id="current-rate">{{ $local->hourly_price }} MAD/h</span>
                                                    @else
                                                        <span class="h6">{{ $local->price }} MAD/réservation</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-calculator me-2"></i>Facturation</h5>
                                                <span id="billing-unit" class="h6">
                                                    @if($local->pricing_type === 'flexible')
                                                        1 × {{ $local->hourly_price }} MAD/h
                                                    @else
                                                        Prix fixe
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-euro-sign me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    @if($local->pricing_type === 'flexible')
                                                        {{ $local->hourly_price ?? $local->price }} MAD
                                                    @else
                                                        {{ $local->price }} MAD
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '{{ $local->pricing_type }}';
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }},
        yearly: {{ $local->yearly_price ?? 0 }}
    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        let rateText = '';

        switch(option) {
            case 'hourly':
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
                break;
            case 'daily':
                if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'weekly':
                if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'monthly':
                if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'yearly':
                if (prices.yearly > 0) {
                    rateText = `${prices.yearly} MAD/an`;
                } else if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois (tarif mensuel)`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'fixed':
                rateText = `${prices.fixed} MAD/réservation`;
                break;
            default:
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
        }

        currentRateSpan.textContent = rateText;
    }

    function calculateDurationAndCost() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const selectedOption = getSelectedPricingOption();

        console.log('Selected pricing option:', selectedOption); // Debug log
        console.log('Available prices:', prices); // Debug log

        if (startTime && endTime) {
            const start = new Date('2000-01-01 ' + startTime);
            const end = new Date('2000-01-01 ' + endTime);

            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);

                let total = 0;
                let durationText = '';
                let billingUnit = '';

                if (pricingType === 'flexible') {
                    // Calculate based on selected pricing option
                    switch(selectedOption) {
                        case 'hourly':
                            if (prices.hourly > 0) {
                                total = diffHours * prices.hourly;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                                billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h';
                                console.log('Hourly calculation:', diffHours, 'x', prices.hourly, '=', total);
                            }
                            break;
                        case 'daily':
                            if (prices.daily > 0) {
                                const days = Math.ceil(diffHours / 24);
                                total = days * prices.daily;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ')';
                                billingUnit = 'Facturation: ' + days + ' × ' + prices.daily + ' MAD/jour';
                                console.log('Daily calculation:', days, 'x', prices.daily, '=', total);
                            } else {
                                // Fallback to hourly if daily not available
                                total = diffHours * prices.hourly;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                                billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                                console.log('Daily fallback to hourly:', diffHours, 'x', prices.hourly, '=', total);
                            }
                            break;
                        case 'weekly':
                            if (prices.weekly > 0) {
                                const weeks = Math.ceil(diffHours / (24 * 7));
                                const days = Math.ceil(diffHours / 24);
                                total = weeks * prices.weekly;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + weeks + ' semaine' + (weeks > 1 ? 's' : '') + ')';
                                billingUnit = 'Facturation: ' + weeks + ' × ' + prices.weekly + ' MAD/semaine';
                                console.log('Weekly calculation:', weeks, 'x', prices.weekly, '=', total);
                            } else {
                                // Fallback to daily or hourly
                                if (prices.daily > 0) {
                                    const days = Math.ceil(diffHours / 24);
                                    total = days * prices.daily;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ')';
                                    billingUnit = 'Facturation: ' + days + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                                } else {
                                    total = diffHours * prices.hourly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                                    billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                                }
                                console.log('Weekly fallback calculation:', total);
                            }
                            break;
                        case 'monthly':
                            if (prices.monthly > 0) {
                                const months = Math.ceil(diffHours / (24 * 30));
                                const days = Math.ceil(diffHours / 24);
                                total = months * prices.monthly;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + months + ' mois)';
                                billingUnit = 'Facturation: ' + months + ' × ' + prices.monthly + ' MAD/mois';
                                console.log('Monthly calculation:', months, 'x', prices.monthly, '=', total);
                            } else {
                                // Fallback to weekly, daily, or hourly
                                if (prices.weekly > 0) {
                                    const weeks = Math.ceil(diffHours / (24 * 7));
                                    const days = Math.ceil(diffHours / 24);
                                    total = weeks * prices.weekly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + weeks + ' semaine' + (weeks > 1 ? 's' : '') + ')';
                                    billingUnit = 'Facturation: ' + weeks + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                                } else if (prices.daily > 0) {
                                    const days = Math.ceil(diffHours / 24);
                                    total = days * prices.daily;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ')';
                                    billingUnit = 'Facturation: ' + days + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                                } else {
                                    total = diffHours * prices.hourly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                                    billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                                }
                                console.log('Monthly fallback calculation:', total);
                            }
                            break;
                        case 'yearly':
                            if (prices.yearly > 0) {
                                const years = Math.ceil(diffHours / (24 * 365));
                                const months = Math.ceil(diffHours / (24 * 30));
                                const days = Math.ceil(diffHours / 24);
                                total = years * prices.yearly;
                                durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + months + ' mois, ' + years + ' an' + (years > 1 ? 's' : '') + ')';
                                billingUnit = 'Facturation: ' + years + ' × ' + prices.yearly + ' MAD/an';
                                console.log('Yearly calculation:', years, 'x', prices.yearly, '=', total);
                            } else {
                                // Fallback to monthly, weekly, daily, or hourly
                                if (prices.monthly > 0) {
                                    const months = Math.ceil(diffHours / (24 * 30));
                                    const days = Math.ceil(diffHours / 24);
                                    total = months * prices.monthly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + months + ' mois)';
                                    billingUnit = 'Facturation: ' + months + ' × ' + prices.monthly + ' MAD/mois (tarif mensuel)';
                                } else if (prices.weekly > 0) {
                                    const weeks = Math.ceil(diffHours / (24 * 7));
                                    const days = Math.ceil(diffHours / 24);
                                    total = weeks * prices.weekly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ', ' + weeks + ' semaine' + (weeks > 1 ? 's' : '') + ')';
                                    billingUnit = 'Facturation: ' + weeks + ' × ' + prices.weekly + ' MAD/semaine (tarif hebdomadaire)';
                                } else if (prices.daily > 0) {
                                    const days = Math.ceil(diffHours / 24);
                                    total = days * prices.daily;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '') + ' (' + days + ' jour' + (days > 1 ? 's' : '') + ')';
                                    billingUnit = 'Facturation: ' + days + ' × ' + prices.daily + ' MAD/jour (tarif journalier)';
                                } else {
                                    total = diffHours * prices.hourly;
                                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                                    billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h (tarif horaire)';
                                }
                                console.log('Yearly fallback calculation:', total);
                            }
                            break;
                        default:
                            total = diffHours * prices.hourly;
                            durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                            billingUnit = 'Facturation: ' + diffHours.toFixed(1) + ' × ' + prices.hourly + ' MAD/h';
                            console.log('Default hourly calculation:', diffHours, 'x', prices.hourly, '=', total);
                    }
                } else {
                    // Fixed pricing
                    total = prices.fixed;
                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                    billingUnit = 'Facturation: Prix fixe';
                    console.log('Fixed pricing:', total);
                }

                durationSpan.textContent = durationText;
                totalSpan.textContent = total.toFixed(2) + ' MAD';

                // Update billing unit display
                const billingUnitSpan = document.getElementById('billing-unit');
                if (billingUnitSpan) {
                    billingUnitSpan.textContent = billingUnit;
                }

                updateCurrentRateDisplay(selectedOption);
            } else {
                durationSpan.textContent = '0 heure';
                totalSpan.textContent = '0 MAD';
                const billingUnitSpan = document.getElementById('billing-unit');
                if (billingUnitSpan) {
                    billingUnitSpan.textContent = '';
                }
            }
        }
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Add event listeners for pricing option changes
    const pricingOptions = document.querySelectorAll('input[name="pricing_option"]');
    const selectedPricingTypeInput = document.getElementById('selected_pricing_type');

    pricingOptions.forEach(option => {
        option.addEventListener('change', function() {
            console.log('Pricing option changed to:', this.value); // Debug log
            calculateDurationAndCost();
            // Update visual selection
            updatePricingSelection();
            // Update hidden input
            if (selectedPricingTypeInput) {
                selectedPricingTypeInput.value = this.value;
            }
        });
    });

    // Update pricing visual selection
    function updatePricingSelection() {
        const allLabels = document.querySelectorAll('.pricing-option-simple');
        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');

        allLabels.forEach(label => {
            label.classList.remove('selected');
        });

        if (selectedOption) {
            const selectedLabel = selectedOption.closest('.pricing-option-simple');
            if (selectedLabel) {
                selectedLabel.classList.add('selected');
            }
        }
    }

    // Initial calculation and selection
    calculateDurationAndCost();
    updatePricingSelection();

    // Check availability
    document.getElementById('checkAvailability').addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const resultDiv = document.getElementById('availabilityResult');

        if (!date || !startTime || !endTime) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez remplir tous les champs de date et heure.</div>';
            return;
        }

        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification en cours...</div>';

        // Simulate API call
        setTimeout(function() {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Créneau disponible !</div>';
        }, 1000);
    });
});
</script>
@endpush

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Simple Pricing Options Styling */
.pricing-option-simple {
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0;
}

.pricing-option-simple:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-option-simple.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.pricing-option-simple input[type="radio"] {
    display: none;
}

.pricing-simple {
    text-align: center;
}

.pricing-simple strong {
    color: #007bff;
    font-size: 1.1rem;
}

.pricing-simple small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .pricing-option-simple {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}
</style>
@endpush
@endsection
