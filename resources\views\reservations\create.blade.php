@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item"><a href="{{ route('locals.show', $local) }}">{{ $local->name }}</a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf
                        <input type="hidden" name="local_id" value="{{ $local->id }}">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="@if($local->pricing_type === 'flexible')hourly @else fixed @endif">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation
                                </label>
                                <input type="date"
                                       class="form-control @error('date') is-invalid @enderror"
                                       id="date"
                                       name="date"
                                       value="{{ old('date', now()->addDay()->format('Y-m-d')) }}"
                                       min="{{ now()->format('Y-m-d') }}"
                                       required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de début
                                </label>
                                <input type="time"
                                       class="form-control @error('start_time') is-invalid @enderror"
                                       id="start_time"
                                       name="start_time"
                                       value="{{ old('start_time', '09:00') }}"
                                       required>
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de fin
                                </label>
                                <input type="time"
                                       class="form-control @error('end_time') is-invalid @enderror"
                                       id="end_time"
                                       name="end_time"
                                       value="{{ old('end_time', '10:00') }}"
                                       required>
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Pricing Type Selection (for flexible pricing) -->
                        @if($local->pricing_type === 'flexible')
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-coins me-2"></i>Choisissez votre type de tarification</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @if($local->hourly_price)
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="hourly_option" value="hourly" checked>
                                                    <label class="form-check-label w-100" for="hourly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-clock text-primary"></i>
                                                                <h6 class="mt-2 mb-1">Tarif horaire</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-primary">{{ $local->hourly_price }} MAD</span>
                                                                <small class="text-muted d-block">par heure</small>
                                                            </div>
                                                            <small class="text-muted">Idéal pour les réunions courtes</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            @endif

                                            @if($local->daily_price)
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="daily_option" value="daily">
                                                    <label class="form-check-label w-100" for="daily_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-sun text-warning"></i>
                                                                <h6 class="mt-2 mb-1">Tarif journalier</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-warning">{{ $local->daily_price }} MAD</span>
                                                                <small class="text-muted d-block">par jour</small>
                                                            </div>
                                                            <small class="text-muted">Parfait pour les ateliers</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            @endif

                                            @if($local->weekly_price)
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="weekly_option" value="weekly">
                                                    <label class="form-check-label w-100" for="weekly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-calendar-week text-success"></i>
                                                                <h6 class="mt-2 mb-1">Tarif hebdomadaire</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-success">{{ $local->weekly_price }} MAD</span>
                                                                <small class="text-muted d-block">par semaine</small>
                                                            </div>
                                                            <small class="text-muted">Idéal pour les projets</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            @endif

                                            @if($local->monthly_price)
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="monthly_option" value="monthly">
                                                    <label class="form-check-label w-100" for="monthly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-calendar-alt text-info"></i>
                                                                <h6 class="mt-2 mb-1">Tarif mensuel</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-info">{{ $local->monthly_price }} MAD</span>
                                                                <small class="text-muted d-block">par mois</small>
                                                            </div>
                                                            <small class="text-muted">Pour les locations longues</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h4">1 heure</span>
                                            </div>
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-tag me-2"></i>Tarification sélectionnée</h5>
                                                <div id="selected-pricing">
                                                    @if($local->pricing_type === 'flexible')
                                                        <span class="h6" id="current-rate">{{ $local->hourly_price }} MAD/h</span>
                                                    @else
                                                        <span class="h6">{{ $local->price }} MAD/réservation</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-calculator me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    @if($local->pricing_type === 'flexible')
                                                        {{ $local->hourly_price ?? $local->price }} MAD
                                                    @else
                                                        {{ $local->price }} MAD
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('locals.show', $local) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($local->image)
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $local->image) }}"
                                     alt="{{ $local->name }}"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    @if($local->type === 'sport')
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    @elseif($local->type === 'conference')
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    @endif
                                </span>
                            </div>
                        @else
                            <!-- Fallback to icon if no image -->
                            @if($local->type === 'sport')
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            @elseif($local->type === 'conference')
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            @else
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            @endif
                        @endif
                    </div>

                    <h5 class="text-center">{{ $local->name }}</h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            {{ $local->location }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : {{ $local->capacity }} personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : {{ ucfirst($local->type) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            @if($local->pricing_type === 'flexible')
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    @if($local->hourly_price)
                                        <small class="d-block">• {{ $local->hourly_price }} MAD/heure</small>
                                    @endif
                                    @if($local->daily_price)
                                        <small class="d-block">• {{ $local->daily_price }} MAD/jour</small>
                                    @endif
                                    @if($local->weekly_price)
                                        <small class="d-block">• {{ $local->weekly_price }} MAD/semaine</small>
                                    @endif
                                    @if($local->monthly_price)
                                        <small class="d-block">• {{ $local->monthly_price }} MAD/mois</small>
                                    @endif
                                </div>
                            @else
                                Prix fixe : {{ $local->price }} MAD/réservation
                            @endif
                        </li>
                    </ul>

                    @if($local->equipment && count($local->equipment) > 0)
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach($local->equipment as $equipment)
                            <span class="badge bg-light text-dark">
                                @if($equipment === 'wifi')
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                @elseif($equipment === 'projecteur')
                                    <i class="fas fa-video me-1"></i>Projecteur
                                @elseif($equipment === 'climatisation')
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                @else
                                    {{ $equipment }}
                                @endif
                            </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '{{ $local->pricing_type }}';
    const prices = {
        fixed: {{ $local->price ?? 0 }},
        hourly: {{ $local->hourly_price ?? 0 }},
        daily: {{ $local->daily_price ?? 0 }},
        weekly: {{ $local->weekly_price ?? 0 }},
        monthly: {{ $local->monthly_price ?? 0 }}
    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        const rateTexts = {
            hourly: `${prices.hourly} MAD/h`,
            daily: `${prices.daily} MAD/jour`,
            weekly: `${prices.weekly} MAD/semaine`,
            monthly: `${prices.monthly} MAD/mois`,
            fixed: `${prices.fixed} MAD/réservation`
        };

        currentRateSpan.textContent = rateTexts[option] || rateTexts.hourly;
    }

    function calculateDurationAndCost() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const selectedOption = getSelectedPricingOption();

        if (startTime && endTime) {
            const start = new Date('2000-01-01 ' + startTime);
            const end = new Date('2000-01-01 ' + endTime);

            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);

                let total = 0;
                let durationText = '';

                if (pricingType === 'flexible') {
                    // Calculate based on selected pricing option
                    switch(selectedOption) {
                        case 'hourly':
                            total = diffHours * prices.hourly;
                            durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                            break;
                        case 'daily':
                            const days = Math.ceil(diffHours / 24);
                            total = days * prices.daily;
                            durationText = days + ' jour' + (days > 1 ? 's' : '');
                            break;
                        case 'weekly':
                            const weeks = Math.ceil(diffHours / (24 * 7));
                            total = weeks * prices.weekly;
                            durationText = weeks + ' semaine' + (weeks > 1 ? 's' : '');
                            break;
                        case 'monthly':
                            const months = Math.ceil(diffHours / (24 * 30));
                            total = months * prices.monthly;
                            durationText = months + ' mois';
                            break;
                        default:
                            total = diffHours * prices.hourly;
                            durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                    }
                } else {
                    // Fixed pricing
                    total = prices.fixed;
                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                }

                durationSpan.textContent = durationText;
                totalSpan.textContent = total.toFixed(2) + ' MAD';
                updateCurrentRateDisplay(selectedOption);
            } else {
                durationSpan.textContent = '0 heure';
                totalSpan.textContent = '0 MAD';
            }
        }
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Add event listeners for pricing option changes
    const pricingOptions = document.querySelectorAll('input[name="pricing_option"]');
    const selectedPricingTypeInput = document.getElementById('selected_pricing_type');

    pricingOptions.forEach(option => {
        option.addEventListener('change', function() {
            calculateDurationAndCost();
            // Update visual selection
            updatePricingCardSelection();
            // Update hidden input
            if (selectedPricingTypeInput) {
                selectedPricingTypeInput.value = this.value;
            }
        });
    });

    // Update pricing card visual selection
    function updatePricingCardSelection() {
        const allCards = document.querySelectorAll('.pricing-card');
        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');

        allCards.forEach(card => {
            card.classList.remove('selected');
        });

        if (selectedOption) {
            const selectedCard = selectedOption.closest('.pricing-option').querySelector('.pricing-card');
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        }
    }

    // Initial calculation and selection
    calculateDurationAndCost();
    updatePricingCardSelection();

    // Check availability
    document.getElementById('checkAvailability').addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const resultDiv = document.getElementById('availabilityResult');

        if (!date || !startTime || !endTime) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez remplir tous les champs de date et heure.</div>';
            return;
        }

        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification en cours...</div>';

        // Simulate API call
        setTimeout(function() {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Créneau disponible !</div>';
        }, 1000);
    });
});
</script>
@endpush

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Pricing Cards Styling */
.pricing-option {
    margin-bottom: 0;
}

.pricing-option .form-check-input {
    display: none;
}

.pricing-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.pricing-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.pricing-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.pricing-header {
    margin-bottom: 1rem;
}

.pricing-header i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.pricing-price {
    margin-bottom: 1rem;
}

.pricing-card h6 {
    font-weight: 600;
    color: #333;
}

.pricing-card .h5 {
    font-weight: 700;
    margin-bottom: 0.25rem;
}

/* Responsive pricing cards */
@media (max-width: 768px) {
    .pricing-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .pricing-header i {
        font-size: 1.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}

/* Animation for pricing selection */
.pricing-card {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endpush
@endsection
