<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar bg-gradient-primary">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white">
                    <span><i class="fas fa-store me-2"></i>Tableau de bord vendeur</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active bg-white text-primary rounded mx-2" href="<?php echo e(route('seller.locals')); ?>">
                            <i class="fas fa-building me-2"></i> Mes locaux
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.locals.create')); ?>">
                            <i class="fas fa-plus me-2"></i> Ajouter un nouveau local
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white-50 hover-white" href="<?php echo e(route('seller.reservations')); ?>">
                            <i class="fas fa-calendar-check me-2"></i> Réservations
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4 bg-light min-vh-100">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                <div>
                    <h1 class="h2 text-primary mb-1">
                        <i class="fas fa-building me-2"></i>Mes locaux
                    </h1>
                    <p class="text-muted">Gérez vos espaces de location</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo e(route('seller.locals.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Ajouter un nouveau local
                    </a>
                </div>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <?php if($locals->count() > 0): ?>
                    <?php $__currentLoopData = $locals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $local): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card shadow-lg border-0 rounded-3 h-100">
                                <!-- Local Image -->
                                <div class="position-relative">
                                    <?php if($local->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $local->image)); ?>" 
                                             alt="<?php echo e($local->name); ?>" 
                                             class="card-img-top"
                                             style="height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Status Badge -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <?php if($local->status): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Actif
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Inactif
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Type Badge -->
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-tag me-1"></i><?php echo e(ucfirst($local->type)); ?>

                                        </span>
                                    </div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title text-primary mb-2"><?php echo e($local->name); ?></h5>
                                    <p class="card-text text-muted small mb-3">
                                        <?php echo e(Str::limit($local->description, 100)); ?>

                                    </p>

                                    <!-- Local Info -->
                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                            <small class="text-muted"><?php echo e($local->location); ?></small>
                                        </div>
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="fas fa-users text-muted me-2"></i>
                                            <small class="text-muted"><?php echo e($local->capacity); ?> personnes</small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-euro-sign text-muted me-2"></i>
                                            <small class="text-muted">
                                                <?php if($local->pricing_type === 'flexible'): ?>
                                                    <?php if($local->hourly_price): ?>
                                                        <?php echo e($local->hourly_price); ?> MAD/h
                                                    <?php else: ?>
                                                        Tarification flexible
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <?php echo e($local->price); ?> MAD/réservation
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Statistics -->
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="text-primary mb-0"><?php echo e($local->reservations->count()); ?></h6>
                                                <small class="text-muted">Réservations</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="text-success mb-0"><?php echo e($local->reservations->where('status', 'confirmée')->count()); ?></h6>
                                                <small class="text-muted">Confirmées</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <h6 class="text-warning mb-0"><?php echo e($local->reservations->where('status', 'en_attente')->count()); ?></h6>
                                            <small class="text-muted">En attente</small>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="mt-auto">
                                        <div class="d-flex gap-2">
                                            <a href="<?php echo e(route('seller.locals.show', $local)); ?>" 
                                               class="btn btn-outline-primary btn-sm flex-fill">
                                                <i class="fas fa-eye me-1"></i>Voir
                                            </a>
                                            <a href="<?php echo e(route('seller.locals.edit', $local)); ?>" 
                                               class="btn btn-primary btn-sm flex-fill">
                                                <i class="fas fa-edit me-1"></i>Modifier
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-outline-danger btn-sm"
                                                    onclick="confirmDelete(<?php echo e($local->id); ?>, '<?php echo e($local->name); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <!-- Empty State -->
                    <div class="col-12">
                        <div class="card shadow-lg border-0 rounded-3">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-building fa-4x text-muted mb-4"></i>
                                <h4 class="text-muted mb-3">Aucun local pour le moment</h4>
                                <p class="text-muted mb-4">
                                    Commencez par ajouter votre premier local pour commencer à recevoir des réservations.
                                </p>
                                <a href="<?php echo e(route('seller.locals.create')); ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>Ajouter mon premier local
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if($locals->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($locals->links()); ?>

                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le local <strong id="localName"></strong> ?</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(localId, localName) {
    document.getElementById('localName').textContent = localName;
    document.getElementById('deleteForm').action = `/seller/locals/${localId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<style>
/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #007bff !important;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.hover-white:hover {
    color: white !important;
}

/* Main Content */
main {
    margin-left: 16.66667%;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-img-top {
    border-radius: 15px 15px 0 0;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    transform: translateY(-2px);
}

/* Badge Styling */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        padding: 1rem 0;
    }

    main {
        margin-left: 0;
    }
    
    .card-body {
        padding: 1rem;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/seller/locals/index.blade.php ENDPATH**/ ?>