<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Conversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'user_message',
        'bot_response',
        'context_data',
        'conversation_type',
        'is_helpful',
        'feedback',
    ];

    protected function casts(): array
    {
        return [
            'context_data' => 'array',
            'is_helpful' => 'boolean',
        ];
    }

    /**
     * Get the user that owns the conversation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for conversations by session.
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope for conversations by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('conversation_type', $type);
    }

    /**
     * Scope for helpful conversations.
     */
    public function scopeHelpful($query)
    {
        return $query->where('is_helpful', true);
    }

    /**
     * Get conversation history for a session.
     */
    public static function getSessionHistory($sessionId, $limit = 10)
    {
        return static::bySession($sessionId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();
    }

    /**
     * Mark conversation as helpful.
     */
    public function markAsHelpful($feedback = null): void
    {
        $this->update([
            'is_helpful' => true,
            'feedback' => $feedback,
        ]);
    }

    /**
     * Mark conversation as not helpful.
     */
    public function markAsNotHelpful($feedback = null): void
    {
        $this->update([
            'is_helpful' => false,
            'feedback' => $feedback,
        ]);
    }
}
