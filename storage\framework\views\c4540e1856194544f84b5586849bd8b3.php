<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.index')); ?>">Locaux</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.show', $local)); ?>"><?php echo e($local->name); ?></a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('reservations.store')); ?>" id="reservationForm">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="local_id" value="<?php echo e($local->id); ?>">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="<?php if($local->pricing_type === 'flexible'): ?>hourly <?php else: ?> fixed <?php endif; ?>">
                        <input type="hidden" name="duration_count" id="duration_count" value="1">
                        <input type="hidden" name="duration_unit" id="duration_unit" value="<?php if($local->pricing_type === 'flexible'): ?>hours <?php else: ?> fixed <?php endif; ?>">

                        <!-- Date and Billing Type Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation *
                                </label>
                                <input type="date"
                                       class="form-control <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="date"
                                       name="date"
                                       value="<?php echo e(old('date', now()->addDay()->format('Y-m-d'))); ?>"
                                       min="<?php echo e(now()->format('Y-m-d')); ?>"
                                       required>
                                <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="billing_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Type de facturation *
                                </label>
                                <select class="form-select <?php $__errorArgs = ['billing_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="billing_type"
                                        name="billing_type"
                                        required>
                                    <option value="">Choisir le type</option>
                                    <?php if($local->pricing_type === 'fixed'): ?>
                                        <option value="fixed" selected>Prix fixe - <?php echo e($local->price); ?> MAD</option>
                                    <?php else: ?>
                                        <?php if($local->hourly_price && $local->hourly_price > 0): ?>
                                        <option value="hourly" <?php echo e(old('billing_type') == 'hourly' ? 'selected' : ''); ?>>
                                            Horaire - <?php echo e($local->hourly_price); ?> MAD/heure
                                        </option>
                                        <?php endif; ?>
                                        <?php if($local->daily_price && $local->daily_price > 0): ?>
                                        <option value="daily" <?php echo e(old('billing_type') == 'daily' ? 'selected' : ''); ?>>
                                            Journalier - <?php echo e($local->daily_price); ?> MAD/jour
                                        </option>
                                        <?php endif; ?>
                                        <?php if($local->weekly_price && $local->weekly_price > 0): ?>
                                        <option value="weekly" <?php echo e(old('billing_type') == 'weekly' ? 'selected' : ''); ?>>
                                            Hebdomadaire - <?php echo e($local->weekly_price); ?> MAD/semaine
                                        </option>
                                        <?php endif; ?>
                                        <?php if($local->monthly_price && $local->monthly_price > 0): ?>
                                        <option value="monthly" <?php echo e(old('billing_type') == 'monthly' ? 'selected' : ''); ?>>
                                            Mensuel - <?php echo e($local->monthly_price); ?> MAD/mois
                                        </option>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </select>
                                <?php $__errorArgs = ['billing_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>Quantité *
                                </label>
                                <div class="input-group">
                                    <input type="number"
                                           class="form-control <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="quantity"
                                           name="quantity"
                                           value="<?php echo e(old('quantity', '1')); ?>"
                                           min="1"
                                           max="365"
                                           required>
                                    <span class="input-group-text" id="quantity_unit">unité(s)</span>
                                </div>
                                <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <small class="text-muted" id="quantity_help">Nombre d'unités à réserver</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="fas fa-calculator me-1"></i>Prix total estimé
                                </label>
                                <div class="form-control bg-light text-center">
                                    <strong id="total_price_display" class="text-success fs-5">
                                        <?php if($local->pricing_type === 'fixed'): ?>
                                            <?php echo e($local->price); ?> MAD
                                        <?php else: ?>
                                            <?php echo e($local->hourly_price ?? 0); ?> MAD
                                        <?php endif; ?>
                                    </strong>
                                </div>
                            </div>
                        </div>

                        <!-- Time Selection (only for hourly billing) -->
                        <div class="row mb-4" id="time_selection" style="display: none;">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-play me-1"></i>Heure de début *
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="start_time"
                                       name="start_time"
                                       value="<?php echo e(old('start_time', '09:00')); ?>">
                                <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-stop me-1"></i>Heure de fin *
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="end_time"
                                       name="end_time"
                                       value="<?php echo e(old('end_time', '10:00')); ?>">
                                <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Quick Time Presets (only for hourly billing) -->
                        <div class="row mb-4" id="time_presets" style="display: none;">
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-clock me-1"></i>Créneaux prédéfinis
                                </label>
                                <div class="row g-2">
                                    <div class="col-6 col-md-3">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="setTimeSlot('08:00', '12:00')">
                                            <i class="fas fa-sun me-1"></i>Matin<br><small>8h - 12h</small>
                                        </button>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="setTimeSlot('12:00', '14:00')">
                                            <i class="fas fa-utensils me-1"></i>Midi<br><small>12h - 14h</small>
                                        </button>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="setTimeSlot('14:00', '18:00')">
                                            <i class="fas fa-sun me-1"></i>Après-midi<br><small>14h - 18h</small>
                                        </button>
                                    </div>
                                    <div class="col-6 col-md-3">
                                        <button type="button" class="btn btn-outline-dark btn-sm w-100" onclick="setTimeSlot('18:00', '22:00')">
                                            <i class="fas fa-moon me-1"></i>Soirée<br><small>18h - 22h</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- All Day Option (for daily billing) -->
                        <div class="row mb-4" id="all_day_option" style="display: none;">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_day" name="all_day" value="1"
                                           <?php echo e(old('all_day') ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="all_day">
                                        <i class="fas fa-sun me-1"></i>Réserver toute la journée (00:00 - 23:59)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Simple Pricing Selection -->
                        <?php if($local->pricing_type === 'flexible'): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-gradient-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Choisissez votre tarif</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php if($local->hourly_price): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="hourly" checked>
                                                    <div class="pricing-simple">
                                                        <strong><?php echo e($local->hourly_price); ?> MAD/heure</strong>
                                                        <small class="d-block text-muted">Idéal pour réunions courtes</small>
                                                    </div>
                                                </label>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->daily_price && $local->daily_price > 0): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="daily">
                                                    <div class="pricing-simple">
                                                        <strong><?php echo e($local->daily_price); ?> MAD/jour</strong>
                                                        <small class="d-block text-muted">Parfait pour ateliers</small>
                                                    </div>
                                                </label>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->weekly_price && $local->weekly_price > 0): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="weekly">
                                                    <div class="pricing-simple">
                                                        <strong><?php echo e($local->weekly_price); ?> MAD/semaine</strong>
                                                        <small class="d-block text-muted">Idéal pour projets</small>
                                                    </div>
                                                </label>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->monthly_price && $local->monthly_price > 0): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="monthly">
                                                    <div class="pricing-simple">
                                                        <strong><?php echo e($local->monthly_price); ?> MAD/mois</strong>
                                                        <small class="d-block text-muted">Pour locations longues</small>
                                                    </div>
                                                </label>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->yearly_price && $local->yearly_price > 0): ?>
                                            <div class="col-md-6 mb-3">
                                                <label class="pricing-option-simple">
                                                    <input type="radio" name="pricing_option" value="yearly">
                                                    <div class="pricing-simple">
                                                        <strong><?php echo e($local->yearly_price); ?> MAD/an</strong>
                                                        <small class="d-block text-muted">Contrats annuels</small>
                                                    </div>
                                                </label>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Duration Input Based on Selected Pricing -->
                        <div class="row mb-4" id="duration-input-section">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-gradient-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Durée de location</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Hourly Duration Input -->
                                        <div id="hourly-duration" class="duration-input-group">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="hours_count" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Nombre d'heures
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="hours_count"
                                                               name="hours_count"
                                                               min="1"
                                                               max="24"
                                                               value="1"
                                                               step="0.5">
                                                        <span class="input-group-text">heure(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 24 heures</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif horaire:</strong> <?php echo e($local->hourly_price); ?> MAD/heure
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Daily Duration Input -->
                                        <div id="daily-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="days_count" class="form-label">
                                                        <i class="fas fa-calendar-day me-1"></i>Nombre de jours
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="days_count"
                                                               name="days_count"
                                                               min="1"
                                                               max="30"
                                                               value="1">
                                                        <span class="input-group-text">jour(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 30 jours</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif journalier:</strong> <?php echo e($local->daily_price); ?> MAD/jour
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Weekly Duration Input -->
                                        <div id="weekly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="weeks_count" class="form-label">
                                                        <i class="fas fa-calendar-week me-1"></i>Nombre de semaines
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="weeks_count"
                                                               name="weeks_count"
                                                               min="1"
                                                               max="12"
                                                               value="1">
                                                        <span class="input-group-text">semaine(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 12 semaines</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif hebdomadaire:</strong> <?php echo e($local->weekly_price); ?> MAD/semaine
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Monthly Duration Input -->
                                        <div id="monthly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="months_count" class="form-label">
                                                        <i class="fas fa-calendar-alt me-1"></i>Nombre de mois
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="months_count"
                                                               name="months_count"
                                                               min="1"
                                                               max="12"
                                                               value="1">
                                                        <span class="input-group-text">mois</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 12 mois</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif mensuel:</strong> <?php echo e($local->monthly_price); ?> MAD/mois
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Yearly Duration Input -->
                                        <div id="yearly-duration" class="duration-input-group" style="display: none;">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <label for="years_count" class="form-label">
                                                        <i class="fas fa-calendar me-1"></i>Nombre d'années
                                                    </label>
                                                    <div class="input-group">
                                                        <input type="number"
                                                               class="form-control"
                                                               id="years_count"
                                                               name="years_count"
                                                               min="1"
                                                               max="5"
                                                               value="1">
                                                        <span class="input-group-text">année(s)</span>
                                                    </div>
                                                    <small class="text-muted">Entre 1 et 5 années</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <strong>Tarif annuel:</strong> <?php echo e($local->yearly_price); ?> MAD/an
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h5">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        1 heure
                                                    <?php else: ?>
                                                        1 réservation
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-tag me-2"></i>Tarif</h5>
                                                <div id="selected-pricing">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        <span class="h6" id="current-rate"><?php echo e($local->hourly_price); ?> MAD/h</span>
                                                    <?php else: ?>
                                                        <span class="h6"><?php echo e($local->price); ?> MAD/réservation</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-calculator me-2"></i>Facturation</h5>
                                                <span id="billing-unit" class="h6">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        1 × <?php echo e($local->hourly_price); ?> MAD/h
                                                    <?php else: ?>
                                                        Prix fixe
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <h5><i class="fas fa-euro-sign me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        <?php echo e($local->hourly_price ?? $local->price); ?> MAD
                                                    <?php else: ?>
                                                        <?php echo e($local->price); ?> MAD
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('locals.show', $local)); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php if($local->image): ?>
                            <div class="position-relative">
                                <img src="<?php echo e(asset('storage/' . $local->image)); ?>"
                                     alt="<?php echo e($local->name); ?>"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    <?php if($local->type === 'sport'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    <?php elseif($local->type === 'conference'): ?>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        <?php else: ?>
                            <!-- Fallback to icon if no image -->
                            <?php if($local->type === 'sport'): ?>
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            <?php elseif($local->type === 'conference'): ?>
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            <?php else: ?>
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>

                    <h5 class="text-center"><?php echo e($local->name); ?></h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            <?php echo e($local->location); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : <?php echo e($local->capacity); ?> personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : <?php echo e(ucfirst($local->type)); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            <?php if($local->pricing_type === 'flexible'): ?>
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    <?php if($local->hourly_price): ?>
                                        <small class="d-block">• <?php echo e($local->hourly_price); ?> MAD/heure</small>
                                    <?php endif; ?>
                                    <?php if($local->daily_price): ?>
                                        <small class="d-block">• <?php echo e($local->daily_price); ?> MAD/jour</small>
                                    <?php endif; ?>
                                    <?php if($local->weekly_price): ?>
                                        <small class="d-block">• <?php echo e($local->weekly_price); ?> MAD/semaine</small>
                                    <?php endif; ?>
                                    <?php if($local->monthly_price): ?>
                                        <small class="d-block">• <?php echo e($local->monthly_price); ?> MAD/mois</small>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                Prix fixe : <?php echo e($local->price); ?> MAD/réservation
                            <?php endif; ?>
                        </li>
                    </ul>

                    <?php if($local->equipment && count($local->equipment) > 0): ?>
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="badge bg-light text-dark">
                                <?php if($equipment === 'wifi'): ?>
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                <?php elseif($equipment === 'projecteur'): ?>
                                    <i class="fas fa-video me-1"></i>Projecteur
                                <?php elseif($equipment === 'climatisation'): ?>
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                <?php else: ?>
                                    <?php echo e($equipment); ?>

                                <?php endif; ?>
                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '<?php echo e($local->pricing_type); ?>';
    const prices = {
        fixed: <?php echo e($local->price ?? 0); ?>,
        hourly: <?php echo e($local->hourly_price ?? 0); ?>,
        daily: <?php echo e($local->daily_price ?? 0); ?>,
        weekly: <?php echo e($local->weekly_price ?? 0); ?>,
        monthly: <?php echo e($local->monthly_price ?? 0); ?>,
        yearly: <?php echo e($local->yearly_price ?? 0); ?>

    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        let rateText = '';

        switch(option) {
            case 'hourly':
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
                break;
            case 'daily':
                if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'weekly':
                if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'monthly':
                if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'yearly':
                if (prices.yearly > 0) {
                    rateText = `${prices.yearly} MAD/an`;
                } else if (prices.monthly > 0) {
                    rateText = `${prices.monthly} MAD/mois (tarif mensuel)`;
                } else if (prices.weekly > 0) {
                    rateText = `${prices.weekly} MAD/semaine (tarif hebdomadaire)`;
                } else if (prices.daily > 0) {
                    rateText = `${prices.daily} MAD/jour (tarif journalier)`;
                } else if (prices.hourly > 0) {
                    rateText = `${prices.hourly} MAD/h (tarif horaire)`;
                } else {
                    rateText = 'Non disponible';
                }
                break;
            case 'fixed':
                rateText = `${prices.fixed} MAD/réservation`;
                break;
            default:
                rateText = prices.hourly > 0 ? `${prices.hourly} MAD/h` : 'Non disponible';
        }

        currentRateSpan.textContent = rateText;
    }

    // Add event listeners
    const billingTypeSelect = document.getElementById('billing_type');
    const quantityInput = document.getElementById('quantity');
    const timeSelection = document.getElementById('time_selection');
    const timePresets = document.getElementById('time_presets');
    const allDayOption = document.getElementById('all_day_option');
    const totalPriceDisplay = document.getElementById('total_price_display');
    const quantityUnit = document.getElementById('quantity_unit');
    const quantityHelp = document.getElementById('quantity_help');

    // Handle billing type change
    function handleBillingTypeChange() {
        const selectedType = billingTypeSelect.value;

        // Hide all conditional sections
        timeSelection.style.display = 'none';
        timePresets.style.display = 'none';
        allDayOption.style.display = 'none';

        // Update quantity unit and help text
        switch(selectedType) {
            case 'hourly':
                quantityUnit.textContent = 'heure(s)';
                quantityHelp.textContent = 'Nombre d\'heures à réserver';
                timeSelection.style.display = 'block';
                timePresets.style.display = 'block';
                quantityInput.max = 24;
                break;
            case 'daily':
                quantityUnit.textContent = 'jour(s)';
                quantityHelp.textContent = 'Nombre de jours à réserver';
                allDayOption.style.display = 'block';
                quantityInput.max = 365;
                break;
            case 'weekly':
                quantityUnit.textContent = 'semaine(s)';
                quantityHelp.textContent = 'Nombre de semaines à réserver';
                quantityInput.max = 52;
                break;
            case 'monthly':
                quantityUnit.textContent = 'mois';
                quantityHelp.textContent = 'Nombre de mois à réserver';
                quantityInput.max = 12;
                break;
            case 'fixed':
                quantityUnit.textContent = 'réservation(s)';
                quantityHelp.textContent = 'Prix fixe par réservation';
                quantityInput.max = 1;
                quantityInput.value = 1;
                break;
        }

        // Update hidden fields
        document.getElementById('selected_pricing_type').value = selectedType;
        document.getElementById('duration_unit').value = selectedType;

        calculatePrice();
    }

    // Calculate price based on selection
    function calculatePrice() {
        const selectedType = billingTypeSelect.value;
        const quantity = parseInt(quantityInput.value) || 1;
        let price = 0;

        if (!selectedType) {
            totalPriceDisplay.textContent = '0 MAD';
            return;
        }

        switch(selectedType) {
            case 'hourly':
                price = (local.hourly_price || 0) * quantity;
                break;
            case 'daily':
                price = (local.daily_price || 0) * quantity;
                break;
            case 'weekly':
                price = (local.weekly_price || 0) * quantity;
                break;
            case 'monthly':
                price = (local.monthly_price || 0) * quantity;
                break;
            case 'fixed':
                price = (local.price || 0) * quantity;
                break;
        }

        totalPriceDisplay.textContent = Math.round(price) + ' MAD';
        document.getElementById('duration_count').value = quantity;
    }

    // Quick time slot function
    function setTimeSlot(startTime, endTime) {
        document.getElementById('start_time').value = startTime;
        document.getElementById('end_time').value = endTime;

        // Calculate hours automatically for hourly billing
        if (billingTypeSelect.value === 'hourly') {
            const start = new Date(`2000-01-01T${startTime}`);
            const end = new Date(`2000-01-01T${endTime}`);
            const diffMs = end - start;
            const diffHours = diffMs / (1000 * 60 * 60);

            if (diffHours > 0) {
                quantityInput.value = diffHours;
                calculatePrice();
            }
        }
    }

    // Make function global for onclick handlers
    window.setTimeSlot = setTimeSlot;

    // Add event listeners
    if (billingTypeSelect) {
        billingTypeSelect.addEventListener('change', handleBillingTypeChange);
    }
    if (quantityInput) {
        quantityInput.addEventListener('input', calculatePrice);
    }

    // Initialize form
    if (billingTypeSelect.value) {
        handleBillingTypeChange();
    } else if (local.pricing_type === 'fixed') {
        // Auto-select fixed pricing if it's the only option
        billingTypeSelect.value = 'fixed';
        handleBillingTypeChange();
    }
});

// Simple availability check function
const checkBtn = document.getElementById('checkAvailability');
if (checkBtn) {
    checkBtn.addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const billingType = document.getElementById('billing_type').value;
        const resultDiv = document.getElementById('availabilityResult');
        const localId = local.id;

        // Clear previous results
        resultDiv.innerHTML = '';

        // Validate inputs
        if (!date) {
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez sélectionner une date.</div>';
            return;
        }

        if (!billingType) {
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Veuillez choisir le type de facturation.</div>';
            return;
        }

        // Show loading state
        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification de la disponibilité...</div>';

        // Prepare request data based on billing type
        let requestData = { date: date };

        if (billingType === 'hourly') {
            const startTime = document.getElementById('start_time').value || '09:00';
            const endTime = document.getElementById('end_time').value || '10:00';

            if (endTime <= startTime) {
                resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>L\'heure de fin doit être après l\'heure de début.</div>';
                return;
            }

            requestData.start_time = startTime;
            requestData.end_time = endTime;
            requestData.all_day = false;
        } else {
            // For other billing types, check general availability
            const allDay = document.getElementById('all_day');
            if (allDay && allDay.checked) {
                requestData.start_time = '00:00';
                requestData.end_time = '23:59';
                requestData.all_day = true;
            } else {
                requestData.start_time = '00:00';
                requestData.end_time = '23:59';
                requestData.all_day = true;
            }
        }

        console.log('Checking availability with data:', requestData);

        // Make API call
        fetch(`/api/locals/${localId}/check-availability`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Availability response:', data);

            if (data.available) {
                let timeInfo = '';
                if (billingType === 'hourly') {
                    timeInfo = `de ${requestData.start_time} à ${requestData.end_time}`;
                } else {
                    timeInfo = 'pour la période sélectionnée';
                }

                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">✅ Disponible !</h6>
                                <p class="mb-0">Le local est disponible ${timeInfo}.</p>
                                ${data.message ? `<small class="text-muted">${data.message}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-times-circle fa-2x text-danger me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">❌ Non disponible</h6>
                                <p class="mb-0">${data.message || 'Ce créneau n\'est pas disponible.'}</p>
                                <small class="text-muted">Veuillez choisir une autre date ou un autre horaire.</small>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error checking availability:', error);
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">⚠️ Erreur</h6>
                            <p class="mb-0">Erreur lors de la vérification de disponibilité.</p>
                            <small class="text-muted">Veuillez réessayer.</small>
                        </div>
                    </div>
                </div>
            `;
        });
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Simple Pricing Options Styling */
.pricing-option-simple {
    display: block;
    cursor: pointer;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0;
}

.pricing-option-simple:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pricing-option-simple.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.pricing-option-simple input[type="radio"] {
    display: none;
}

.pricing-simple {
    text-align: center;
}

.pricing-simple strong {
    color: #007bff;
    font-size: 1.1rem;
}

.pricing-simple small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .pricing-option-simple {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/reservations/create.blade.php ENDPATH**/ ?>