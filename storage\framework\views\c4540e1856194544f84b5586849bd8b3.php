<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.index')); ?>">Locaux</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.show', $local)); ?>"><?php echo e($local->name); ?></a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('reservations.store')); ?>" id="reservationForm">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="local_id" value="<?php echo e($local->id); ?>">
                        <input type="hidden" name="selected_pricing_type" id="selected_pricing_type" value="<?php if($local->pricing_type === 'flexible'): ?>hourly <?php else: ?> fixed <?php endif; ?>">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation
                                </label>
                                <input type="date"
                                       class="form-control <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="date"
                                       name="date"
                                       value="<?php echo e(old('date', now()->addDay()->format('Y-m-d'))); ?>"
                                       min="<?php echo e(now()->format('Y-m-d')); ?>"
                                       required>
                                <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de début
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="start_time"
                                       name="start_time"
                                       value="<?php echo e(old('start_time', '09:00')); ?>"
                                       required>
                                <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de fin
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="end_time"
                                       name="end_time"
                                       value="<?php echo e(old('end_time', '10:00')); ?>"
                                       required>
                                <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Pricing Type Selection (for flexible pricing) -->
                        <?php if($local->pricing_type === 'flexible'): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fas fa-coins me-2"></i>Choisissez votre type de tarification</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php if($local->hourly_price): ?>
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="hourly_option" value="hourly" checked>
                                                    <label class="form-check-label w-100" for="hourly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-clock text-primary"></i>
                                                                <h6 class="mt-2 mb-1">Tarif horaire</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-primary"><?php echo e($local->hourly_price); ?> MAD</span>
                                                                <small class="text-muted d-block">par heure</small>
                                                            </div>
                                                            <small class="text-muted">Idéal pour les réunions courtes</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->daily_price): ?>
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="daily_option" value="daily">
                                                    <label class="form-check-label w-100" for="daily_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-sun text-warning"></i>
                                                                <h6 class="mt-2 mb-1">Tarif journalier</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-warning"><?php echo e($local->daily_price); ?> MAD</span>
                                                                <small class="text-muted d-block">par jour</small>
                                                            </div>
                                                            <small class="text-muted">Parfait pour les ateliers</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->weekly_price): ?>
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="weekly_option" value="weekly">
                                                    <label class="form-check-label w-100" for="weekly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-calendar-week text-success"></i>
                                                                <h6 class="mt-2 mb-1">Tarif hebdomadaire</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-success"><?php echo e($local->weekly_price); ?> MAD</span>
                                                                <small class="text-muted d-block">par semaine</small>
                                                            </div>
                                                            <small class="text-muted">Idéal pour les projets</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if($local->monthly_price): ?>
                                            <div class="col-md-6 col-lg-3 mb-3">
                                                <div class="form-check pricing-option">
                                                    <input class="form-check-input" type="radio" name="pricing_option" id="monthly_option" value="monthly">
                                                    <label class="form-check-label w-100" for="monthly_option">
                                                        <div class="pricing-card">
                                                            <div class="pricing-header">
                                                                <i class="fas fa-calendar-alt text-info"></i>
                                                                <h6 class="mt-2 mb-1">Tarif mensuel</h6>
                                                            </div>
                                                            <div class="pricing-price">
                                                                <span class="h5 text-info"><?php echo e($local->monthly_price); ?> MAD</span>
                                                                <small class="text-muted d-block">par mois</small>
                                                            </div>
                                                            <small class="text-muted">Pour les locations longues</small>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-gradient-primary text-white">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-clock me-2"></i>Durée</h5>
                                                <span id="duration" class="h4">1 heure</span>
                                            </div>
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-tag me-2"></i>Tarification sélectionnée</h5>
                                                <div id="selected-pricing">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        <span class="h6" id="current-rate"><?php echo e($local->hourly_price); ?> MAD/h</span>
                                                    <?php else: ?>
                                                        <span class="h6"><?php echo e($local->price); ?> MAD/réservation</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h5><i class="fas fa-calculator me-2"></i>Total</h5>
                                                <span id="total" class="h4">
                                                    <?php if($local->pricing_type === 'flexible'): ?>
                                                        <?php echo e($local->hourly_price ?? $local->price); ?> MAD
                                                    <?php else: ?>
                                                        <?php echo e($local->price); ?> MAD
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('locals.show', $local)); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php if($local->image): ?>
                            <div class="position-relative">
                                <img src="<?php echo e(asset('storage/' . $local->image)); ?>"
                                     alt="<?php echo e($local->name); ?>"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    <?php if($local->type === 'sport'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    <?php elseif($local->type === 'conference'): ?>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        <?php else: ?>
                            <!-- Fallback to icon if no image -->
                            <?php if($local->type === 'sport'): ?>
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            <?php elseif($local->type === 'conference'): ?>
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            <?php else: ?>
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>

                    <h5 class="text-center"><?php echo e($local->name); ?></h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            <?php echo e($local->location); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : <?php echo e($local->capacity); ?> personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : <?php echo e(ucfirst($local->type)); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            <?php if($local->pricing_type === 'flexible'): ?>
                                Tarification flexible :
                                <div class="ms-3 mt-1">
                                    <?php if($local->hourly_price): ?>
                                        <small class="d-block">• <?php echo e($local->hourly_price); ?> MAD/heure</small>
                                    <?php endif; ?>
                                    <?php if($local->daily_price): ?>
                                        <small class="d-block">• <?php echo e($local->daily_price); ?> MAD/jour</small>
                                    <?php endif; ?>
                                    <?php if($local->weekly_price): ?>
                                        <small class="d-block">• <?php echo e($local->weekly_price); ?> MAD/semaine</small>
                                    <?php endif; ?>
                                    <?php if($local->monthly_price): ?>
                                        <small class="d-block">• <?php echo e($local->monthly_price); ?> MAD/mois</small>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                Prix fixe : <?php echo e($local->price); ?> MAD/réservation
                            <?php endif; ?>
                        </li>
                    </ul>

                    <?php if($local->equipment && count($local->equipment) > 0): ?>
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="badge bg-light text-dark">
                                <?php if($equipment === 'wifi'): ?>
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                <?php elseif($equipment === 'projecteur'): ?>
                                    <i class="fas fa-video me-1"></i>Projecteur
                                <?php elseif($equipment === 'climatisation'): ?>
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                <?php else: ?>
                                    <?php echo e($equipment); ?>

                                <?php endif; ?>
                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const currentRateSpan = document.getElementById('current-rate');

    // Pricing data
    const pricingType = '<?php echo e($local->pricing_type); ?>';
    const prices = {
        fixed: <?php echo e($local->price ?? 0); ?>,
        hourly: <?php echo e($local->hourly_price ?? 0); ?>,
        daily: <?php echo e($local->daily_price ?? 0); ?>,
        weekly: <?php echo e($local->weekly_price ?? 0); ?>,
        monthly: <?php echo e($local->monthly_price ?? 0); ?>

    };

    // Get selected pricing option
    function getSelectedPricingOption() {
        if (pricingType === 'fixed') return 'fixed';

        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');
        return selectedOption ? selectedOption.value : 'hourly';
    }

    // Update current rate display
    function updateCurrentRateDisplay(option) {
        if (!currentRateSpan) return;

        const rateTexts = {
            hourly: `${prices.hourly} MAD/h`,
            daily: `${prices.daily} MAD/jour`,
            weekly: `${prices.weekly} MAD/semaine`,
            monthly: `${prices.monthly} MAD/mois`,
            fixed: `${prices.fixed} MAD/réservation`
        };

        currentRateSpan.textContent = rateTexts[option] || rateTexts.hourly;
    }

    function calculateDurationAndCost() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const selectedOption = getSelectedPricingOption();

        if (startTime && endTime) {
            const start = new Date('2000-01-01 ' + startTime);
            const end = new Date('2000-01-01 ' + endTime);

            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);

                let total = 0;
                let durationText = '';

                if (pricingType === 'flexible') {
                    // Calculate based on selected pricing option
                    switch(selectedOption) {
                        case 'hourly':
                            total = diffHours * prices.hourly;
                            durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                            break;
                        case 'daily':
                            const days = Math.ceil(diffHours / 24);
                            total = days * prices.daily;
                            durationText = days + ' jour' + (days > 1 ? 's' : '');
                            break;
                        case 'weekly':
                            const weeks = Math.ceil(diffHours / (24 * 7));
                            total = weeks * prices.weekly;
                            durationText = weeks + ' semaine' + (weeks > 1 ? 's' : '');
                            break;
                        case 'monthly':
                            const months = Math.ceil(diffHours / (24 * 30));
                            total = months * prices.monthly;
                            durationText = months + ' mois';
                            break;
                        default:
                            total = diffHours * prices.hourly;
                            durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                    }
                } else {
                    // Fixed pricing
                    total = prices.fixed;
                    durationText = diffHours.toFixed(1) + ' heure' + (diffHours > 1 ? 's' : '');
                }

                durationSpan.textContent = durationText;
                totalSpan.textContent = total.toFixed(2) + ' MAD';
                updateCurrentRateDisplay(selectedOption);
            } else {
                durationSpan.textContent = '0 heure';
                totalSpan.textContent = '0 MAD';
            }
        }
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Add event listeners for pricing option changes
    const pricingOptions = document.querySelectorAll('input[name="pricing_option"]');
    const selectedPricingTypeInput = document.getElementById('selected_pricing_type');

    pricingOptions.forEach(option => {
        option.addEventListener('change', function() {
            calculateDurationAndCost();
            // Update visual selection
            updatePricingCardSelection();
            // Update hidden input
            if (selectedPricingTypeInput) {
                selectedPricingTypeInput.value = this.value;
            }
        });
    });

    // Update pricing card visual selection
    function updatePricingCardSelection() {
        const allCards = document.querySelectorAll('.pricing-card');
        const selectedOption = document.querySelector('input[name="pricing_option"]:checked');

        allCards.forEach(card => {
            card.classList.remove('selected');
        });

        if (selectedOption) {
            const selectedCard = selectedOption.closest('.pricing-option').querySelector('.pricing-card');
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        }
    }

    // Initial calculation and selection
    calculateDurationAndCost();
    updatePricingCardSelection();

    // Check availability
    document.getElementById('checkAvailability').addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const resultDiv = document.getElementById('availabilityResult');

        if (!date || !startTime || !endTime) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez remplir tous les champs de date et heure.</div>';
            return;
        }

        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification en cours...</div>';

        // Simulate API call
        setTimeout(function() {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Créneau disponible !</div>';
        }, 1000);
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.badge {
    border-radius: 6px;
    font-weight: 500;
}

.sticky-top {
    position: sticky !important;
    top: 20px !important;
}

/* Pricing Cards Styling */
.pricing-option {
    margin-bottom: 0;
}

.pricing-option .form-check-input {
    display: none;
}

.pricing-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.pricing-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.pricing-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.pricing-header {
    margin-bottom: 1rem;
}

.pricing-header i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.pricing-price {
    margin-bottom: 1rem;
}

.pricing-card h6 {
    font-weight: 600;
    color: #333;
}

.pricing-card .h5 {
    font-weight: 700;
    margin-bottom: 0.25rem;
}

/* Responsive pricing cards */
@media (max-width: 768px) {
    .pricing-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .pricing-header i {
        font-size: 1.5rem;
    }

    .sticky-top {
        position: relative !important;
        top: 0 !important;
    }
}

/* Animation for pricing selection */
.pricing-card {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/reservations/create.blade.php ENDPATH**/ ?>