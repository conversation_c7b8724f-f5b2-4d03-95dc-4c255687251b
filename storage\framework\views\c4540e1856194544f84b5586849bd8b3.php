<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Accueil</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.index')); ?>">Locaux</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('locals.show', $local)); ?>"><?php echo e($local->name); ?></a></li>
            <li class="breadcrumb-item active">Réservation</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Reservation Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Nouvelle réservation
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('reservations.store')); ?>" id="reservationForm">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="local_id" value="<?php echo e($local->id); ?>">

                        <!-- Date Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="date" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de réservation
                                </label>
                                <input type="date"
                                       class="form-control <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="date"
                                       name="date"
                                       value="<?php echo e(old('date', now()->addDay()->format('Y-m-d'))); ?>"
                                       min="<?php echo e(now()->format('Y-m-d')); ?>"
                                       required>
                                <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de début
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="start_time"
                                       name="start_time"
                                       value="<?php echo e(old('start_time', '09:00')); ?>"
                                       required>
                                <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">
                                    <i class="fas fa-clock me-1"></i>Heure de fin
                                </label>
                                <input type="time"
                                       class="form-control <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="end_time"
                                       name="end_time"
                                       value="<?php echo e(old('end_time', '10:00')); ?>"
                                       required>
                                <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Duration and Cost Display -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-4">
                                                <h5 class="text-primary">Durée</h5>
                                                <span id="duration" class="h4">1 heure</span>
                                            </div>
                                            <div class="col-md-4">
                                                <h5 class="text-success">Prix unitaire</h5>
                                                <span class="h4"><?php echo e($local->price); ?>MAD/h</span>
                                            </div>
                                            <div class="col-md-4">
                                                <h5 class="text-warning">Total</h5>
                                                <span id="total" class="h4"><?php echo e($local->price); ?>MAD</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Check -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-info" id="checkAvailability">
                                    <i class="fas fa-search me-2"></i>Vérifier la disponibilité
                                </button>
                                <div id="availabilityResult" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a>
                                        et la <a href="#" class="text-decoration-none">politique de remboursement</a>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('locals.show', $local)); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-credit-card me-2"></i>Procéder au paiement
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Local Summary -->
            <div class="card sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Récapitulatif
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php if($local->image): ?>
                            <div class="position-relative">
                                <img src="<?php echo e(asset('storage/' . $local->image)); ?>"
                                     alt="<?php echo e($local->name); ?>"
                                     class="img-fluid rounded shadow-sm mb-2"
                                     style="max-height: 200px; width: 100%; object-fit: cover;">
                                <!-- Type badge overlay -->
                                <span class="position-absolute top-0 end-0 m-2">
                                    <?php if($local->type === 'sport'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-futbol me-1"></i>Sport
                                        </span>
                                    <?php elseif($local->type === 'conference'): ?>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-presentation-screen me-1"></i>Conférence
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-glass-cheers me-1"></i>Fête
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        <?php else: ?>
                            <!-- Fallback to icon if no image -->
                            <?php if($local->type === 'sport'): ?>
                                <i class="fas fa-futbol text-success fa-3x mb-2"></i>
                            <?php elseif($local->type === 'conference'): ?>
                                <i class="fas fa-presentation-screen text-primary fa-3x mb-2"></i>
                            <?php else: ?>
                                <i class="fas fa-glass-cheers text-warning fa-3x mb-2"></i>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>

                    <h5 class="text-center"><?php echo e($local->name); ?></h5>

                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            <?php echo e($local->location); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            Capacité : <?php echo e($local->capacity); ?> personnes
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            Type : <?php echo e(ucfirst($local->type)); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-euro-sign text-muted me-2"></i>
                            Prix : <?php echo e($local->price); ?>MAD/heure
                        </li>
                    </ul>

                    <?php if($local->equipment && count($local->equipment) > 0): ?>
                    <hr>
                    <h6>Équipements inclus :</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <?php $__currentLoopData = $local->equipment; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equipment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="badge bg-light text-dark">
                                <?php if($equipment === 'wifi'): ?>
                                    <i class="fas fa-wifi me-1"></i>WiFi
                                <?php elseif($equipment === 'projecteur'): ?>
                                    <i class="fas fa-video me-1"></i>Projecteur
                                <?php elseif($equipment === 'climatisation'): ?>
                                    <i class="fas fa-snowflake me-1"></i>Clim
                                <?php else: ?>
                                    <?php echo e($equipment); ?>

                                <?php endif; ?>
                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Besoin d'aide ?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2">
                        <strong>Comment ça marche :</strong>
                    </p>
                    <ol class="small">
                        <li>Choisissez votre créneau</li>
                        <li>Vérifiez la disponibilité</li>
                        <li>Procédez au paiement</li>
                        <li>Recevez votre confirmation</li>
                    </ol>
                    <button type="button" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-envelope me-1"></i>Nous contacter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const durationSpan = document.getElementById('duration');
    const totalSpan = document.getElementById('total');
    const pricePerHour = <?php echo e($local->price); ?>;

    function calculateDurationAndCost() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;

        if (startTime && endTime) {
            const start = new Date('2000-01-01 ' + startTime);
            const end = new Date('2000-01-01 ' + endTime);

            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);
                const total = diffHours * pricePerHour;

                durationSpan.textContent = diffHours + ' heure' + (diffHours > 1 ? 's' : '');
                totalSpan.textContent = total.toFixed(2) + 'MAD';
            } else {
                durationSpan.textContent = '0 heure';
                totalSpan.textContent = '0MAD';
            }
        }
    }

    startTimeInput.addEventListener('change', calculateDurationAndCost);
    endTimeInput.addEventListener('change', calculateDurationAndCost);

    // Check availability
    document.getElementById('checkAvailability').addEventListener('click', function() {
        const date = document.getElementById('date').value;
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const resultDiv = document.getElementById('availabilityResult');

        if (!date || !startTime || !endTime) {
            resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez remplir tous les champs de date et heure.</div>';
            return;
        }

        resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Vérification en cours...</div>';

        // Simulate API call
        setTimeout(function() {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Créneau disponible !</div>';
        }, 1000);
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/reservations/create.blade.php ENDPATH**/ ?>