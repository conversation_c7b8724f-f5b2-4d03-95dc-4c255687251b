<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Local extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'location',
        'capacity',
        'price',
        'equipment',
        'image',
        'status',
        'seller_id',
        'pricing_structure',
        'pricing_type',
        'hourly_price',
        'daily_price',
        'weekly_price',
        'monthly_price',
        'yearly_price',
        'minimum_rental_hours',
        'maximum_rental_hours',
        'available_24_7',
        'available_hours',
        'rental_terms',
        'approval_required',
    ];

    protected function casts(): array
    {
        return [
            'equipment' => 'array',
            'status' => 'boolean',
            'price' => 'decimal:2',
            'pricing_structure' => 'array',
            'hourly_price' => 'decimal:2',
            'daily_price' => 'decimal:2',
            'weekly_price' => 'decimal:2',
            'monthly_price' => 'decimal:2',
            'yearly_price' => 'decimal:2',
            'available_24_7' => 'boolean',
            'available_hours' => 'array',
        ];
    }

    /**
     * Get the reservations for the local.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Check if local is available for a specific date and time range.
     */
    public function isAvailable($date, $startTime, $endTime, $excludeReservationId = null): bool
    {
        $query = $this->reservations()
            ->where('date', $date)
            ->where('status', '!=', 'annulée')
            ->where(function ($q) use ($startTime, $endTime) {
                $q->whereBetween('start_time', [$startTime, $endTime])
                  ->orWhereBetween('end_time', [$startTime, $endTime])
                  ->orWhere(function ($q2) use ($startTime, $endTime) {
                      $q2->where('start_time', '<=', $startTime)
                         ->where('end_time', '>=', $endTime);
                  });
            });

        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        return $query->count() === 0;
    }

    /**
     * Scope for active locals.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope for filtering by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the seller that owns the local.
     */
    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    /**
     * Calculate price based on rental duration and type.
     */
    public function calculatePrice($hours, $rentalType = 'hourly')
    {
        switch ($rentalType) {
            case 'hourly':
                return $this->hourly_price * $hours;
            case 'daily':
                $days = ceil($hours / 24);
                return $this->daily_price * $days;
            case 'weekly':
                $weeks = ceil($hours / (24 * 7));
                return $this->weekly_price * $weeks;
            case 'monthly':
                $months = ceil($hours / (24 * 30));
                return $this->monthly_price * $months;
            case 'yearly':
                $years = ceil($hours / (24 * 365));
                return $this->yearly_price * $years;
            default:
                return $this->price; // Fallback to base price
        }
    }

    /**
     * Get the best pricing option for given duration.
     */
    public function getBestPrice($hours)
    {
        $prices = [];

        if ($this->hourly_price) {
            $prices['hourly'] = $this->hourly_price * $hours;
        }

        if ($this->daily_price && $hours >= 24) {
            $days = ceil($hours / 24);
            $prices['daily'] = $this->daily_price * $days;
        }

        if ($this->weekly_price && $hours >= (24 * 7)) {
            $weeks = ceil($hours / (24 * 7));
            $prices['weekly'] = $this->weekly_price * $weeks;
        }

        if ($this->monthly_price && $hours >= (24 * 30)) {
            $months = ceil($hours / (24 * 30));
            $prices['monthly'] = $this->monthly_price * $months;
        }

        if ($this->yearly_price && $hours >= (24 * 365)) {
            $years = ceil($hours / (24 * 365));
            $prices['yearly'] = $this->yearly_price * $years;
        }

        if (empty($prices)) {
            return ['type' => 'fixed', 'price' => $this->price];
        }

        $bestOption = array_keys($prices, min($prices))[0];
        return ['type' => $bestOption, 'price' => $prices[$bestOption]];
    }

    /**
     * Check if local is available at specific time.
     */
    public function isAvailableAtTime($dateTime)
    {
        if ($this->available_24_7) {
            return true;
        }

        if (!$this->available_hours) {
            return true; // No restrictions
        }

        $dayOfWeek = strtolower($dateTime->format('l'));
        $time = $dateTime->format('H:i');

        if (isset($this->available_hours[$dayOfWeek])) {
            $dayHours = $this->available_hours[$dayOfWeek];
            return $time >= $dayHours['start'] && $time <= $dayHours['end'];
        }

        return false;
    }

    /**
     * Scope for seller's locals.
     */
    public function scopeBySeller($query, $sellerId)
    {
        return $query->where('seller_id', $sellerId);
    }

    /**
     * Scope for verified seller locals.
     */
    public function scopeByVerifiedSellers($query)
    {
        return $query->whereHas('seller', function ($q) {
            $q->where('verified_seller', true);
        });
    }
}
