<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Gestion des types de locaux
                    </h4>
                    <a href="<?php echo e(route('admin.local-types.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Ajouter un type
                    </a>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Ordre</th>
                                    <th>Nom</th>
                                    <th>Nom FR</th>
                                    <th>Icône</th>
                                    <th>Couleur</th>
                                    <th>Locaux</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e($type->sort_order); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo e($type->name); ?></strong>
                                            <?php if($type->description): ?>
                                                <br><small class="text-muted"><?php echo e($type->description); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($type->name_fr ?? '-'); ?></td>
                                        <td>
                                            <i class="<?php echo e($type->icon); ?>" style="color: <?php echo e($type->color); ?>; font-size: 1.2rem;"></i>
                                            <small class="d-block text-muted"><?php echo e($type->icon); ?></small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="color-preview me-2" style="width: 20px; height: 20px; background-color: <?php echo e($type->color); ?>; border-radius: 3px; border: 1px solid #ddd;"></div>
                                                <code><?php echo e($type->color); ?></code>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($type->locals_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <form action="<?php echo e(route('admin.local-types.toggle-status', $type)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" class="btn btn-sm <?php echo e($type->active ? 'btn-success' : 'btn-secondary'); ?>">
                                                    <i class="fas <?php echo e($type->active ? 'fa-check' : 'fa-times'); ?>"></i>
                                                    <?php echo e($type->active ? 'Actif' : 'Inactif'); ?>

                                                </button>
                                            </form>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.local-types.edit', $type)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if($type->locals_count == 0): ?>
                                                    <form action="<?php echo e(route('admin.local-types.destroy', $type)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce type ?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-outline-secondary" disabled title="Impossible de supprimer - <?php echo e($type->locals_count); ?> locaux utilisent ce type">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Aucun type de local trouvé.</p>
                                            <a href="<?php echo e(route('admin.local-types.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Créer le premier type
                                            </a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.color-preview {
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\N\test\locaspace\resources\views/admin/local-types/index.blade.php ENDPATH**/ ?>