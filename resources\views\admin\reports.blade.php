@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Rapports</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Rapports et Analytics
                    </h1>
                    <p class="text-muted">Vue d'ensemble des performances de la plateforme</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue by Month -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Revenus par mois (12 derniers mois)
                    </h5>
                </div>
                <div class="card-body">
                    @if($revenueByMonth->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th class="text-end">Revenus (MAD)</th>
                                        <th class="text-end">Évolution</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($revenueByMonth as $index => $revenue)
                                    <tr>
                                        <td>
                                            <strong>{{ \Carbon\Carbon::parse($revenue->month . '-01')->format('F Y') }}</strong>
                                        </td>
                                        <td class="text-end">
                                            <span class="badge bg-success fs-6">{{ number_format(abs($revenue->total), 2) }} MAD</span>
                                        </td>
                                        <td class="text-end">
                                            @if($index < $revenueByMonth->count() - 1)
                                                @php
                                                    $previousRevenue = $revenueByMonth[$index + 1]->total;
                                                    $currentRevenue = $revenue->total;
                                                    $evolution = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
                                                @endphp
                                                @if($evolution > 0)
                                                    <span class="text-success">
                                                        <i class="fas fa-arrow-up"></i> +{{ number_format($evolution, 1) }}%
                                                    </span>
                                                @elseif($evolution < 0)
                                                    <span class="text-danger">
                                                        <i class="fas fa-arrow-down"></i> {{ number_format($evolution, 1) }}%
                                                    </span>
                                                @else
                                                    <span class="text-muted">
                                                        <i class="fas fa-minus"></i> 0%
                                                    </span>
                                                @endif
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <th>Total</th>
                                        <th class="text-end">{{ number_format(abs($revenueByMonth->sum('total')), 2) }} MAD</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune donnée de revenus disponible</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Reservations by Type -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Réservations par type de local
                    </h5>
                </div>
                <div class="card-body">
                    @if($reservationsByType->count() > 0)
                        @php $totalReservations = $reservationsByType->sum('count'); @endphp
                        @foreach($reservationsByType as $type)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                @if($type->type === 'sport')
                                    <i class="fas fa-futbol text-success fa-2x me-3"></i>
                                @elseif($type->type === 'conference')
                                    <i class="fas fa-presentation-screen text-primary fa-2x me-3"></i>
                                @else
                                    <i class="fas fa-glass-cheers text-warning fa-2x me-3"></i>
                                @endif
                                <div>
                                    <h6 class="mb-0">{{ ucfirst($type->type) }}</h6>
                                    <small class="text-muted">{{ $type->count }} réservations</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary fs-6">{{ $type->count }}</span>
                                <br>
                                <small class="text-muted">
                                    {{ $totalReservations > 0 ? number_format(($type->count / $totalReservations) * 100, 1) : 0 }}%
                                </small>
                            </div>
                        </div>
                        @endforeach
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total</strong>
                            <strong>{{ $totalReservations }} réservations</strong>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réservation confirmée</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Top Locals by Revenue -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>Top locaux par revenus
                    </h5>
                </div>
                <div class="card-body">
                    @if($topLocalsByRevenue->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($topLocalsByRevenue as $index => $local)
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        @if($index === 0)
                                            <i class="fas fa-trophy text-warning fa-lg"></i>
                                        @elseif($index === 1)
                                            <i class="fas fa-medal text-secondary fa-lg"></i>
                                        @elseif($index === 2)
                                            <i class="fas fa-award text-warning fa-lg"></i>
                                        @else
                                            <span class="badge bg-light text-dark">{{ $index + 1 }}</span>
                                        @endif
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ $local->name }}</h6>
                                        <small class="text-muted">{{ $local->location }}</small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    @if($local->type === 'sport')
                                        <i class="fas fa-futbol text-success me-2"></i>
                                    @elseif($local->type === 'conference')
                                        <i class="fas fa-presentation-screen text-primary me-2"></i>
                                    @else
                                        <i class="fas fa-glass-cheers text-warning me-2"></i>
                                    @endif
                                    <div>
                                        <span class="badge bg-success">{{ number_format(abs($local->total_revenue), 0) }} MAD</span>
                                        <br>
                                        <small class="text-muted">{{ abs($local->price) }} MAD/h</small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun local avec revenus</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Résumé des performances
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-primary">{{ $revenueByMonth->count() }}</h3>
                                <p class="text-muted mb-0">Mois analysés</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-success">{{ number_format(abs($revenueByMonth->sum('total')), 0) }} MAD</h3>
                                <p class="text-muted mb-0">Revenus totaux</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h3 class="text-info">{{ $reservationsByType->sum('count') }}</h3>
                                <p class="text-muted mb-0">Réservations confirmées</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-warning">{{ $topLocalsByRevenue->count() }}</h3>
                            <p class="text-muted mb-0">Locaux performants</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .breadcrumb, .navbar {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }

    .card-header {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
    }
}
</style>
@endsection
